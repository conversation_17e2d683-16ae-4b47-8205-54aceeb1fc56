<?php
require_once '../includes/autoload.php';
Auth::requireCoachingOwner();

$coachingId = $_SESSION['coaching_id'];
$coachingObj = new CoachingCenter();
$coaching = $coachingObj->getById($coachingId);
$userObj = new User();
$user = $userObj->getUserById($_SESSION['user_id']);

$message = '';
$success = false;

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    $data = [
        'coaching_name' => trim($_POST['coaching_name']),
        'email' => trim($_POST['email']),
        'phone' => trim($_POST['phone']),
        'website' => trim($_POST['website']),
        'address' => trim($_POST['address']),
    ];
    // Handle logo upload
    if (!empty($_FILES['logo']['name'])) {
        $uploadDir = '../uploads/coaching_logos/';
        if (!is_dir($uploadDir)) mkdir($uploadDir, 0755, true);
        $fileName = 'logo_' . $coachingId . '_' . time() . '_' . basename($_FILES['logo']['name']);
        $targetFile = $uploadDir . $fileName;
        if (move_uploaded_file($_FILES['logo']['tmp_name'], $targetFile)) {
            $data['logo'] = 'uploads/coaching_logos/' . $fileName;
        }
    }
    // Handle banner upload
    if (!empty($_FILES['banner_image']['name'])) {
        $uploadDir = '../uploads/coaching_banners/';
        if (!is_dir($uploadDir)) mkdir($uploadDir, 0755, true);
        $fileName = 'banner_' . $coachingId . '_' . time() . '_' . basename($_FILES['banner_image']['name']);
        $targetFile = $uploadDir . $fileName;
        if (move_uploaded_file($_FILES['banner_image']['tmp_name'], $targetFile)) {
            $data['banner_image'] = 'uploads/coaching_banners/' . $fileName;
        }
    }
    $updated = $coachingObj->update($coachingId, $data);
    $message = $updated ? '<div class="alert alert-success">Profile updated.</div>' : '<div class="alert alert-danger">Failed to update profile.</div>';
    $coaching = $coachingObj->getById($coachingId);
}

// Handle password change
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_password'])) {
    $currentPassword = $_POST['current_password'];
    $newPassword = $_POST['new_password'];
    $confirmPassword = $_POST['confirm_password'];
    if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
        $message = '<div class="alert alert-danger">All password fields are required.</div>';
    } elseif ($newPassword !== $confirmPassword) {
        $message = '<div class="alert alert-danger">New passwords do not match.</div>';
    } elseif (!$userObj->changePassword($currentPassword, $newPassword)) {
        $message = '<div class="alert alert-danger">Current password is incorrect or failed to update password.</div>';
    } else {
        $message = '<div class="alert alert-success">Password updated successfully.</div>';
    }
}

$pageTitle = 'Settings';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Coaching Panel</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/admin.css'); ?>">
</head>
<body>
    <div class="dashboard-container">
        <?php include 'templates/sidebar.php'; ?>
        <div class="main-content">
            <?php include 'templates/header.php'; ?>
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-12">
                        <h1 class="page-title">Settings</h1>
                        <p class="text-muted">Manage your coaching center's profile and account settings.</p>
                    </div>
                </div>
                <?php echo $message; ?>
                <div class="row">
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Profile Settings</h5>
                            </div>
                            <div class="card-body">
                                <form method="post" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label class="form-label">Coaching Center Name</label>
                                        <input type="text" name="coaching_name" class="form-control" value="<?php echo htmlspecialchars($coaching['coaching_name']); ?>" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Email</label>
                                        <input type="email" name="email" class="form-control" value="<?php echo htmlspecialchars($coaching['email']); ?>" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Phone</label>
                                        <input type="text" name="phone" class="form-control" value="<?php echo htmlspecialchars($coaching['phone']); ?>" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Website</label>
                                        <input type="url" name="website" class="form-control" value="<?php echo htmlspecialchars($coaching['website']); ?>">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Address</label>
                                        <textarea name="address" class="form-control" rows="2"><?php echo htmlspecialchars($coaching['address']); ?></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Logo</label>
                                        <?php if (!empty($coaching['logo'])): ?>
                                            <div class="mb-2"><img src="<?php echo getUploadUrl($coaching['logo']); ?>" alt="Logo" class="img-thumbnail" style="max-width: 100px;"></div>
                                        <?php endif; ?>
                                        <input type="file" name="logo" class="form-control" accept="image/*">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Banner Image</label>
                                        <?php if (!empty($coaching['banner_image'])): ?>
                                            <div class="mb-2"><img src="<?php echo getUploadUrl($coaching['banner_image']); ?>" alt="Banner" class="img-thumbnail" style="max-width: 150px;"></div>
                                        <?php endif; ?>
                                        <input type="file" name="banner_image" class="form-control" accept="image/*">
                                    </div>
                                    <div class="d-grid">
                                        <button type="submit" name="update_profile" class="btn btn-primary">Save Profile</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Change Password</h5>
                            </div>
                            <div class="card-body">
                                <form method="post">
                                    <div class="mb-3">
                                        <label class="form-label">Current Password</label>
                                        <input type="password" name="current_password" class="form-control" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">New Password</label>
                                        <input type="password" name="new_password" class="form-control" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Confirm New Password</label>
                                        <input type="password" name="confirm_password" class="form-control" required>
                                    </div>
                                    <div class="d-grid">
                                        <button type="submit" name="update_password" class="btn btn-primary">Change Password</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
