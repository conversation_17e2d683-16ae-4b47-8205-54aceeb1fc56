<?php
require_once 'includes/autoload.php';

// Get settings
$settings = Settings::getInstance();

// Check current logo setting
echo "Current logo setting: " . $settings->getSiteLogo() . "\n";

// Check if the file exists
$logoPath = 'assets/images/logo/logo.png';
$fullPath = __DIR__ . '/' . $logoPath;
echo "Full path: " . $fullPath . "\n";
echo "File exists: " . (file_exists($fullPath) ? 'Yes' : 'No') . "\n";
echo "File size: " . (file_exists($fullPath) ? filesize($fullPath) . " bytes" : "N/A") . "\n";

// Ensure the logo path is set correctly
$settings->set('site_logo', 'assets/images/logo/logo.png', 'general', true);

// Verify the update
echo "Updated logo setting: " . $settings->getSiteLogo() . "\n";

// Output some debug info about the asset URL
echo "Asset URL: " . getAssetUrl($settings->getSiteLogo()) . "\n";
echo "BASE_URL: " . BASE_URL . "\n";
echo "ASSETS_URL: " . ASSETS_URL . "\n";