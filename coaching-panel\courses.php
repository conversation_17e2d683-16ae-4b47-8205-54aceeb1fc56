<?php
require_once '../includes/autoload.php';
Auth::requireCoachingOwner();

$coachingId = $_SESSION['coaching_id'];
$coachingObj = new CoachingCenter();
$categoryObj = new Category();
$locationObj = new Location();

// Handle add/edit/delete
$message = '';
$editCourse = null;
$courseLocations = [];

if (isset($_GET['action'], $_GET['course_id']) && $_GET['action'] === 'delete') {
    $courseId = (int)$_GET['course_id'];
    $deleted = Database::getInstance()->delete('courses', 'course_id = ? AND coaching_id = ?', [$courseId, $coachingId]);
    $message = $deleted ? '<div class="alert alert-success">Course deleted.</div>' : '<div class="alert alert-danger">Failed to delete course.</div>';
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Prepare data for database
    $data = [
        'coaching_id' => $coachingId,
        'category_id' => (int)$_POST['category_id'],
        'course_name' => trim($_POST['course_name']),
        'duration' => trim($_POST['duration']),
        'fee_structure' => trim($_POST['fee_structure']),
        'status' => isset($_POST['status']) ? $_POST['status'] : 'active',
    ];
    
    // Only include description if it's not empty
    $description = trim(isset($_POST['description']) ? $_POST['description'] : '');
    if (!empty($description)) {
        $data['description'] = $description;
    } else {
        // Set description to NULL to avoid empty string issues
        $data['description'] = null;
    }
    
    // Get selected locations
    $selectedLocations = isset($_POST['locations']) ? $_POST['locations'] : [];
    
    // Validate that at least one location is selected
    if (empty($selectedLocations)) {
        $message = '<div class="alert alert-danger">Please select at least one location for the course.</div>';
        // Skip the rest of the processing
        goto skip_processing;
    }
    
    if (!empty($_POST['edit_id'])) {
        $editId = (int)$_POST['edit_id'];
        $db = Database::getInstance();
        
        // Log the update attempt
        error_log("Attempting to update course ID: {$editId} for coaching ID: {$coachingId}");
        error_log("Course data: " . print_r($data, true));
        error_log("Selected locations: " . print_r($selectedLocations, true));
        
        // First, check if the course exists and belongs to this coaching center
        $courseExists = $db->fetchOne(
            "SELECT COUNT(*) FROM courses WHERE course_id = ? AND coaching_id = ?",
            [$editId, $coachingId]
        );
        
        if (!$courseExists) {
            error_log("Course ID {$editId} does not exist or does not belong to coaching ID {$coachingId}");
            $message = '<div class="alert alert-danger">Course not found or you do not have permission to edit it.</div>';
            goto skip_processing;
        }
        
        // Get the current course data to compare
        $currentCourse = $db->fetchRow("SELECT * FROM courses WHERE course_id = ?", [$editId]);
        error_log("Current course data: " . print_r($currentCourse, true));
        
        // Make sure we have the slug field for the update
        // If the course name has changed, we need to generate a new slug
        if (isset($data['course_name']) && $data['course_name'] !== $currentCourse['course_name']) {
            error_log("Course name changed, generating new slug");
            $data['slug'] = Utility::generateUniqueSlug($data['course_name'], 'courses', 'slug', $editId);
        } else {
            // Use the existing slug
            $data['slug'] = $currentCourse['slug'];
        }
        
        error_log("Using slug: " . $data['slug']);
        
        // Check if there are any changes to the course data
        $hasChanges = false;
        foreach ($data as $key => $value) {
            if (!isset($currentCourse[$key]) || $currentCourse[$key] != $value) {
                $hasChanges = true;
                error_log("Change detected in field '{$key}': '" . (isset($currentCourse[$key]) ? $currentCourse[$key] : 'NULL') . "' -> '{$value}'");
                break;
            }
        }
        
        if (!$hasChanges) {
            error_log("No changes detected in course data, skipping database update");
            // Even if there are no changes to the course data, we still need to update locations
            $locationsUpdated = $coachingObj->updateCourseLocations($editId, $selectedLocations);
            
            if ($locationsUpdated) {
                $message = '<div class="alert alert-success">Course locations updated successfully.</div>';
                error_log("Course locations updated successfully for course ID: {$editId}");
            } else {
                $message = '<div class="alert alert-warning">Failed to update course locations. Please try again.</div>';
                error_log("Failed to update locations for course ID: {$editId}");
            }
            goto skip_processing;
        }
        
        // Perform the update
        error_log("Updating course with data: " . print_r($data, true));
        $updated = $db->update('courses', $data, 'course_id = ? AND coaching_id = ?', [$editId, $coachingId]);
        
        if ($updated) {
            // Update course locations
            $locationsUpdated = $coachingObj->updateCourseLocations($editId, $selectedLocations);
            
            if ($locationsUpdated) {
                $message = '<div class="alert alert-success">Course updated successfully.</div>';
                error_log("Course and locations updated successfully for course ID: {$editId}");
            } else {
                $message = '<div class="alert alert-warning">Course updated but failed to update locations. Please try again.</div>';
                error_log("Course updated but failed to update locations for course ID: {$editId}");
            }
        } else {
            $dbError = $db->getLastError();
            error_log("Failed to update course ID: {$editId}. Error: {$dbError}");
            $message = '<div class="alert alert-danger">Failed to update course. Database error: ' . htmlspecialchars($dbError) . '</div>';
        }
    } else {
        $data['slug'] = Utility::generateUniqueSlug($data['course_name'], 'courses', 'slug');
        $courseId = Database::getInstance()->insert('courses', $data);
        
        if ($courseId) {
            // Add course locations
            $coachingObj->updateCourseLocations($courseId, $selectedLocations);
            $message = '<div class="alert alert-success">Course added.</div>';
        } else {
            $message = '<div class="alert alert-danger">Failed to add course.</div>';
        }
    }
    
    // Label for skipping processing when validation fails
    skip_processing:
}

if (isset($_GET['action'], $_GET['course_id']) && $_GET['action'] === 'edit') {
    $courseId = (int)$_GET['course_id'];
    $editCourse = Database::getInstance()->fetchRow('SELECT * FROM courses WHERE course_id = ? AND coaching_id = ?', [$courseId, $coachingId]);
    
    if ($editCourse) {
        // Get course locations
        $courseLocations = $coachingObj->getCourseLocations($courseId);
    }
}

// Fetch all courses for this coaching center
$courses = $coachingObj->getCourses($coachingId);
$allCategories = $categoryObj->getAll();

// Get all locations for this coaching center
$myLocations = $coachingObj->getLocations($coachingId);

// For each course, get its locations
foreach ($courses as &$course) {
    $course['locations'] = $coachingObj->getCourseLocations($course['course_id']);
}

$pageTitle = 'Manage Courses';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Coaching Panel</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/admin.css'); ?>">
</head>
<body>
    <div class="dashboard-container">
        <?php include 'templates/sidebar.php'; ?>
        <div class="main-content">
            <?php include 'templates/header.php'; ?>
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-12">
                        <h1 class="page-title">Courses</h1>
                        <p class="text-muted">Manage your coaching center's courses here.</p>
                    </div>
                </div>
                <?php echo $message; ?>
                <div class="row">
                    <div class="col-lg-5 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><?php echo $editCourse ? 'Edit Course' : 'Add New Course'; ?></h5>
                            </div>
                            <div class="card-body">
                                <form method="post">
                                    <?php if ($editCourse): ?>
                                        <input type="hidden" name="edit_id" value="<?php echo $editCourse['course_id']; ?>">
                                    <?php endif; ?>
                                    <div class="mb-3">
                                        <label class="form-label">Course Name</label>
                                        <input type="text" name="course_name" class="form-control" value="<?php echo htmlspecialchars(isset($editCourse['course_name']) ? $editCourse['course_name'] : ''); ?>" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Category</label>
                                        <select name="category_id" class="form-select" required>
                                            <option value="">Select Category</option>
                                            <?php foreach ($allCategories as $cat): ?>
                                                <option value="<?php echo $cat['category_id']; ?>" <?php echo (isset($editCourse['category_id']) && $editCourse['category_id'] == $cat['category_id']) ? 'selected' : ''; ?>><?php echo htmlspecialchars($cat['category_name']); ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Description</label>
                                        <textarea name="description" class="form-control" rows="3" placeholder="Enter course description (optional)"><?php echo htmlspecialchars(isset($editCourse['description']) ? $editCourse['description'] : ''); ?></textarea>
                                        <small class="text-muted">Provide a detailed description of the course content and objectives.</small>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Duration</label>
                                        <input type="text" name="duration" class="form-control" value="<?php echo htmlspecialchars(isset($editCourse['duration']) ? $editCourse['duration'] : ''); ?>">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Fee Structure</label>
                                        <input type="text" name="fee_structure" class="form-control" value="<?php echo htmlspecialchars(isset($editCourse['fee_structure']) ? $editCourse['fee_structure'] : ''); ?>">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Status</label>
                                        <select name="status" class="form-select">
                                            <option value="active" <?php echo (isset($editCourse['status']) && $editCourse['status'] == 'active') ? 'selected' : ''; ?>>Active</option>
                                            <option value="inactive" <?php echo (isset($editCourse['status']) && $editCourse['status'] == 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Available at Locations <span class="text-danger">*</span></label>
                                        <select name="locations[]" class="form-select" multiple required>
                                            <?php if (empty($myLocations)): ?>
                                                <option value="" disabled>No locations available. Please add locations first.</option>
                                            <?php else: ?>
                                                <?php foreach ($myLocations as $loc): ?>
                                                    <?php 
                                                        $isSelected = false;
                                                        if (!empty($courseLocations)) {
                                                            foreach ($courseLocations as $courseLoc) {
                                                                if ($courseLoc['location_id'] == $loc['location_id']) {
                                                                    $isSelected = true;
                                                                    break;
                                                                }
                                                            }
                                                        }
                                                    ?>
                                                    <option value="<?php echo $loc['location_id']; ?>" <?php echo $isSelected ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($loc['location_name']); ?> (<?php echo htmlspecialchars(isset($loc['city_name']) ? $loc['city_name'] : ''); ?>)
                                                    </option>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </select>
                                        <small class="text-muted">Hold Ctrl/Cmd to select multiple locations. At least one location is required.</small>
                                    </div>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary"><?php echo $editCourse ? 'Update Course' : 'Add Course'; ?></button>
                                        <?php if ($editCourse): ?>
                                            <a href="courses.php" class="btn btn-secondary mt-2">Cancel</a>
                                        <?php endif; ?>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-7">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Your Courses</h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-striped mb-0">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Category</th>
                                                <th>Duration</th>
                                                <th>Locations</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (!empty($courses)): ?>
                                                <?php foreach ($courses as $course): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($course['course_name']); ?></td>
                                                        <td><?php echo htmlspecialchars(isset($course['category_name']) ? $course['category_name'] : ''); ?></td>
                                                        <td><?php echo htmlspecialchars($course['duration']); ?></td>
                                                        <td>
                                                            <?php if (!empty($course['locations'])): ?>
                                                                <small>
                                                                <?php 
                                                                    $locationNames = array_map(function($loc) {
                                                                        return $loc['location_name'] . ' (' . $loc['city_name'] . ')';
                                                                    }, $course['locations']);
                                                                    echo htmlspecialchars(implode(', ', $locationNames));
                                                                ?>
                                                                </small>
                                                            <?php else: ?>
                                                                <span class="badge bg-warning">No locations</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-<?php echo $course['status'] === 'active' ? 'success' : 'danger'; ?>"><?php echo ucfirst($course['status']); ?></span>
                                                        </td>
                                                        <td>
                                                            <a href="courses.php?action=edit&course_id=<?php echo $course['course_id']; ?>" class="btn btn-sm btn-primary">Edit</a>
                                                            <a href="courses.php?action=delete&course_id=<?php echo $course['course_id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this course?');">Delete</a>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <tr><td colspan="6" class="text-center">No courses found.</td></tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
