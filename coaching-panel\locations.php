<?php
require_once '../includes/autoload.php';
Auth::requireCoachingOwner();

$coachingId = $_SESSION['coaching_id'];
$locationObj = new Location();
$message = '';
$editLocation = null;

// Handle add/delete mapping
if (isset($_GET['action'], $_GET['location_id']) && $_GET['action'] === 'remove') {
    $locationId = (int)$_GET['location_id'];
    $deleted = Database::getInstance()->delete('coaching_locations', 'coaching_id = ? AND location_id = ?', [$coachingId, $locationId]);
    $message = $deleted ? '<div class="alert alert-success">Location removed from your center.</div>' : '<div class="alert alert-danger">Failed to remove location.</div>';
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Create a new location
    if (isset($_POST['form_type']) && $_POST['form_type'] === 'create_location') {
        $locationName = trim($_POST['location_name']);
        $cityId = (int)$_POST['city_id'];
        $pincode = trim($_POST['pincode']);
        $address = trim($_POST['address'] ?? '');
        
        // Validate input
        if (empty($locationName)) {
            $message = '<div class="alert alert-danger">Location name is required.</div>';
        } elseif (empty($cityId)) {
            $message = '<div class="alert alert-danger">Please select a city.</div>';
        } else {
            // Create the location
            $locationData = [
                'location_name' => $locationName,
                'city_id' => $cityId,
                'pincode' => $pincode,
                'address' => $address,
                'status' => 'active'
            ];
            
            $locationId = $locationObj->addLocation($locationData);
            
            if ($locationId) {
                // Automatically add this location to the coaching center
                Database::getInstance()->insert('coaching_locations', [
                    'coaching_id' => $coachingId,
                    'location_id' => $locationId
                ]);
                
                $message = '<div class="alert alert-success">New location added to your center successfully.</div>';
            } else {
                $message = '<div class="alert alert-danger">Failed to create location.</div>';
            }
        }
    }
}

// Fetch all locations (global)
$allLocations = $locationObj->getAllLocations();
// Fetch all locations mapped to this coaching center
$myLocations = Database::getInstance()->fetchAll('SELECT l.*, c.city_name, s.state_name FROM coaching_locations cl JOIN locations l ON cl.location_id = l.location_id LEFT JOIN cities c ON l.city_id = c.city_id LEFT JOIN states s ON c.state_id = s.state_id WHERE cl.coaching_id = ? ORDER BY l.location_name ASC', [$coachingId]);
// Fetch all cities for the dropdown
$allCities = $locationObj->getAllCities();
$pageTitle = 'Manage Locations';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Coaching Panel</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/admin.css'); ?>">
</head>
<body>
    <div class="dashboard-container">
        <?php include 'templates/sidebar.php'; ?>
        <div class="main-content">
            <?php include 'templates/header.php'; ?>
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-12">
                        <h1 class="page-title">Locations</h1>
                        <p class="text-muted">Create new locations or add existing ones to your coaching center. Each location can have different courses available.</p>
                    </div>
                </div>
                <?php echo $message; ?>
                <div class="row">
                    <div class="col-lg-5 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Add New Location</h5>
                            </div>
                            <div class="card-body">
                                <form method="post">
                                    <input type="hidden" name="form_type" value="create_location">
                                    <div class="mb-3">
                                        <label class="form-label">Location Name*</label>
                                        <input type="text" name="location_name" class="form-control" required>
                                        <small class="text-muted">E.g., Sector 18, Koramangala, Andheri East</small>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">City*</label>
                                        <select name="city_id" class="form-select" required>
                                            <option value="">Select City</option>
                                            <?php foreach ($allCities as $city): ?>
                                                <option value="<?php echo $city['city_id']; ?>">
                                                    <?php echo htmlspecialchars($city['city_name']); ?> (<?php echo htmlspecialchars($city['state_name'] ?? ''); ?>)
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Pincode</label>
                                        <input type="text" name="pincode" class="form-control">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Address (Optional)</label>
                                        <textarea name="address" class="form-control" rows="3" placeholder="Full address of this location"></textarea>
                                    </div>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">Add Location</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-7">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Your Locations</h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-striped mb-0">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>City, State</th>
                                                <th>Address</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (!empty($myLocations)): ?>
                                                <?php foreach ($myLocations as $loc): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($loc['location_name']); ?></td>
                                                        <td>
                                                            <?php echo htmlspecialchars($loc['city_name'] ?? 'N/A'); ?>
                                                            <?php if (!empty($loc['state_name'])): ?>, <?php echo htmlspecialchars($loc['state_name']); ?><?php endif; ?>
                                                            <?php if (!empty($loc['pincode'])): ?> - <?php echo htmlspecialchars($loc['pincode']); ?><?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <?php if (!empty($loc['address'])): ?>
                                                                <?php echo nl2br(htmlspecialchars($loc['address'])); ?>
                                                            <?php else: ?>
                                                                <span class="text-muted">No address provided</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <a href="locations.php?action=remove&location_id=<?php echo $loc['location_id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Remove this location from your center?');">Remove</a>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <tr><td colspan="4" class="text-center">No locations mapped to your center yet.</td></tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
