<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include required files
require_once 'includes/autoload.php';

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    redirect('index.php');
}

try {
    // Get coaching ID and validate
    $coaching_id = (int)($_POST['coaching_id'] ?? 0);

    if ($coaching_id <= 0) {
        $_SESSION['enquiry_error'] = "Invalid coaching center.";
        redirect('index.php');
    }

    // Get coaching center details
    $coachingObj = new CoachingCenter();
    $coaching = $coachingObj->getById($coaching_id);

    if (!$coaching || empty($coaching['slug'])) {
        $_SESSION['enquiry_error'] = "Coaching center not found.";
        redirect('index.php');
    }

    // Validate form data
    $errors = [];
    if (empty($_POST['name'])) $errors[] = "Name is required";
    if (empty($_POST['email']) || !filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Valid email is required";
    }
    if (empty($_POST['phone'])) $errors[] = "Phone is required";

    // Get the slug from the form submission if available, otherwise use the one from the database
    $slug = !empty($_POST['slug']) ? $_POST['slug'] : $coaching['slug'];
    
    // Construct redirect URL
    $redirect_url = "center.php?slug=" . urlencode($slug);

    if (!empty($errors)) {
        $_SESSION['enquiry_errors'] = $errors;
        redirect($redirect_url . "&enquiry=error");
    }

    // Save to database
    $db = Database::getInstance();
    $sql = "INSERT INTO enquiries (coaching_id, course_id, location_id, name, email, phone, message, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";

    $params = [
        $coaching_id,
        $_POST['course'] ?? null,
        $_POST['location_id'] ?? null,
        sanitize($_POST['name']),
        sanitize($_POST['email']),
        sanitize($_POST['phone']),
        sanitize($_POST['message'] ?? '')
    ];

    $stmt = $db->query($sql, $params);

    if (!$stmt) {
        $_SESSION['enquiry_error'] = "Failed to submit enquiry. Please try again.";
        redirect($redirect_url . "&enquiry=error");
    }

    $stmt->close();

    // Success
    unset($_SESSION['enquiry_errors']);
    redirect($redirect_url . "&enquiry=success");

} catch (Exception $e) {
    $_SESSION['enquiry_error'] = "An error occurred. Please try again.";
    redirect('index.php');
}


