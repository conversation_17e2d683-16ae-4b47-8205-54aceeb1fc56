<?php
/**
 * Coaching Center Detail Page
 * This file handles the coaching center detail pages
 */
require_once 'includes/autoload.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Get settings
$settings = Settings::getInstance();

// Get coaching slug from URL
$slug = isset($_GET['slug']) ? $_GET['slug'] : '';

// Redirect to home if no slug provided
if (empty($slug)) {
    header('Location: index.php');
    exit;
}

// Get location filter if provided
$locationFilter = isset($_GET['location_id']) ? (int)$_GET['location_id'] : null;

// Get coaching center details
$coachingObj = new CoachingCenter();
$coaching = $coachingObj->getBySlug($slug);

// If coaching center not found, redirect to home
if (!$coaching || !isset($coaching['coaching_id'])) {
    header('Location: index.php');
    exit;
}

// Get courses offered by this coaching center
$courses = [];
if (isset($coaching['coaching_id'])) {
    if ($locationFilter) {
        // If location filter is applied, get courses for that specific location
        $courses = $coachingObj->getCourses($coaching['coaching_id'], $locationFilter);
    } else {
        // Otherwise, get unique courses to avoid duplicates
        $courses = $coachingObj->getUniqueCourses($coaching['coaching_id']);
    }
}

// Get success stories
$successStories = [];
if (isset($coaching['coaching_id'])) {
    $successStories = $coachingObj->getSuccessStories($coaching['coaching_id']);
}

// Get reviews
$reviews = [];
if (isset($coaching['coaching_id'])) { 
    $reviews = $coachingObj->getReviews($coaching['coaching_id']);
}

// Get gallery images
$galleryImages = [];
if (isset($coaching['coaching_id'])) {
    $galleryImages = Database::getInstance()->fetchAll(
        'SELECT * FROM coaching_images WHERE coaching_id = ? ORDER BY display_order ASC, image_id DESC',
        [$coaching['coaching_id']]
    );
}

// Page title and meta
$pageTitle = isset($coaching['coaching_name']) ? $coaching['coaching_name'] : 'Coaching Center';
$pageDescription = isset($coaching['description']) && !empty($coaching['description']) 
    ? substr(strip_tags($coaching['description']), 0, 160) 
    : (isset($coaching['coaching_name']) ? $coaching['coaching_name'] : 'Coaching Center') . ' - Coaching Center';
$pageKeywords = (isset($coaching['coaching_name']) ? $coaching['coaching_name'] : 'Coaching Center') . ', coaching center, ' . $settings->getMetaKeywords();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php echo getMetaTags($pageTitle . ' - ' . $settings->getSiteName(), $pageDescription, $pageKeywords); ?>
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo getAssetUrl($settings->getSiteFavicon()); ?>" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getAssetUrl('css/style.css'); ?>">
    
    <!-- Page-specific styles -->
    <style>
        .course-tags {
            margin-top: 10px;
        }
        
        .location-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .location-tag {
            display: inline-flex;
            align-items: center;
            background-color: #e9f5ff;
            color: #0066cc;
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 12px;
        }
        
        .location-tag i {
            margin-right: 5px;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php include 'templates/header.php'; ?>
    
    <!-- Main Content -->
    <main>
        <!-- Page Header with Banner -->
        <section class="page-header">
            <?php if (!empty($coaching['banner_image'])): ?>
                <div class="coaching-banner">
                    <img src="<?php echo getUploadUrl($coaching['banner_image']); ?>" alt="<?php echo htmlspecialchars($coaching['coaching_name']); ?> Banner" class="img-fluid w-100">
                </div>
            <?php endif; ?>
            <div class="container">
                <h1><?php echo htmlspecialchars($coaching['coaching_name']); ?></h1>
            </div>
        </section>

        <section class="breadcrumb-section">
            <div class="container">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="categories.php">Categories</a></li>
                        <?php if (!empty($coaching['categories'])): ?>
                            <li class="breadcrumb-item"><a href="<?php echo getCategoryUrl($coaching['categories'][0]['category_slug']); ?>"><?php echo htmlspecialchars($coaching['categories'][0]['category_name']); ?></a></li>
                        <?php endif; ?>
                        <li class="breadcrumb-item active" aria-current="page"><?php echo htmlspecialchars($coaching['coaching_name']); ?></li>
                    </ol>
                </nav>
            </div>
        </section>
        
        <!-- Coaching Center Overview -->
        <section class="coaching-overview">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="coaching-detail-card">
                            <div class="coaching-header">
                                <div class="coaching-logo">
                                    <img src="<?php echo isset($coaching['logo']) ? getUploadUrl($coaching['logo']) : getAssetUrl('images/default-logo.png'); ?>" alt="<?php echo htmlspecialchars($coaching['coaching_name']); ?>">
                                </div>
                                <div class="coaching-title">
                                    <h2><?php echo htmlspecialchars($coaching['coaching_name']); ?></h2>
                                    <div class="coaching-meta">
                                        <span><i class="fas fa-map-marker-alt"></i> <?php echo isset($coaching['city_name']) ? htmlspecialchars($coaching['city_name']) : 'N/A'; ?><?php echo isset($coaching['state_name']) ? ', ' . htmlspecialchars($coaching['state_name']) : ''; ?></span>
                                        <span><i class="fas fa-star"></i> <?php echo isset($coaching['avg_rating']) ? number_format($coaching['avg_rating'], 1) : '0.0'; ?> (<?php echo isset($coaching['total_reviews']) ? $coaching['total_reviews'] : '0'; ?> reviews)</span>
                                        <?php if (!empty($coaching['established_year'])): ?>
                                            <span><i class="fas fa-calendar-alt"></i> Est. <?php echo htmlspecialchars($coaching['established_year']); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <?php if (!empty($coaching['locations'])): ?>
                                    <div class="location-tags mt-2">
                                        <?php foreach ($coaching['locations'] as $location): ?>
                                            <a href="?slug=<?php echo urlencode($coaching['slug']); ?>&location_id=<?php echo $location['location_id']; ?>" class="location-tag">
                                                <i class="fas fa-map-pin"></i> <?php echo htmlspecialchars($location['location_name']); ?>, <?php echo htmlspecialchars($location['city_name']); ?>
                                            </a>
                                        <?php endforeach; ?>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <div class="coaching-categories">
                                        <?php if (!empty($coaching['categories'])): ?>
                                            <?php foreach ($coaching['categories'] as $category): ?>
                                                <a href="<?php echo getCategoryUrl($category['category_slug']); ?>" class="category-badge"><?php echo htmlspecialchars($category['category_name']); ?></a>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <span class="category-badge">General</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="coaching-description">
                                <h3>About <?php echo htmlspecialchars($coaching['coaching_name']); ?></h3>
                                <p><?php echo isset($coaching['description']) ? $coaching['description'] : 'No description available.'; ?></p>
                            </div>
                            
                            <div class="coaching-facilities">
                                <h3>Facilities</h3>
                                <div class="facilities-list">
                                    <?php if (!empty($coaching['facilities'])): ?>
                                        <?php foreach ($coaching['facilities'] as $facility): ?>
                                            <div class="facility-item">
                                                <i class="<?php echo !empty($facility['icon']) ? $facility['icon'] : 'fas fa-check-circle'; ?>"></i>
                                                <span><?php echo htmlspecialchars($facility['facility_name']); ?></span>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <p>No facilities information available.</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <?php if (!empty($galleryImages)): ?>
                            <div class="coaching-gallery mt-4">
                                <h3>Gallery</h3>
                                <div class="small-gallery-carousel">
                                    <div id="galleryCarousel" class="carousel slide" data-bs-ride="carousel">
                                        <div class="carousel-inner">
                                            <?php foreach ($galleryImages as $index => $img): ?>
                                                <div class="carousel-item <?php echo $index === 0 ? 'active' : ''; ?>">
                                                    <div class="gallery-frame">
                                                        <img src="<?php echo getUploadUrl($img['image_path']); ?>" class="d-block rounded" alt="Gallery Image" width="250" height="200">
                                                        <?php if (!empty($img['caption'])): ?>
                                                            <div class="carousel-caption d-none d-md-block">
                                                                <p><?php echo htmlspecialchars($img['caption']); ?></p>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                        <button class="carousel-control-prev" type="button" data-bs-target="#galleryCarousel" data-bs-slide="prev">
                                            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                            <span class="visually-hidden">Previous</span>
                                        </button>
                                        <button class="carousel-control-next" type="button" data-bs-target="#galleryCarousel" data-bs-slide="next">
                                            <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                            <span class="visually-hidden">Next</span>
                                        </button>
                                        <div class="carousel-indicators">
                                            <?php foreach ($galleryImages as $index => $img): ?>
                                                <button type="button" data-bs-target="#galleryCarousel" data-bs-slide-to="<?php echo $index; ?>" <?php echo $index === 0 ? 'class="active" aria-current="true"' : ''; ?> aria-label="Slide <?php echo $index + 1; ?>"></button>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Courses Section -->
                        <div class="coaching-detail-card">
                            <h3>
                                Courses Offered
                                <?php if ($locationFilter && !empty($coaching['locations'])): ?>
                                    <?php 
                                        $locationName = '';
                                        foreach ($coaching['locations'] as $loc) {
                                            if ($loc['location_id'] == $locationFilter) {
                                                $locationName = $loc['location_name'] . ', ' . $loc['city_name'];
                                                break;
                                            }
                                        }
                                        if (!empty($locationName)):
                                    ?>
                                    <small class="text-muted">(Available at <?php echo htmlspecialchars($locationName); ?>)</small>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </h3>
                            <div class="courses-list">
                                <?php if (empty($courses)): ?>
                                    <div class="alert alert-info">
                                        <?php if ($locationFilter): ?>
                                            No courses available at this location. <a href="<?php echo "center.php?slug=" . urlencode($coaching['slug']); ?>">View all courses</a>
                                        <?php else: ?>
                                            No courses available.
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($courses as $course): ?>
                                        <div class="course-item">
                                            <div class="course-header">
                                                <h4><?php echo isset($course['course_name']) ? htmlspecialchars($course['course_name']) : 'Unnamed Course'; ?></h4>
                                                <div class="course-meta">
                                                    <?php if (isset($course['duration'])): ?>
                                                        <span><i class="fas fa-clock"></i> <?php echo htmlspecialchars($course['duration']); ?></span>
                                                    <?php endif; ?>
                                                    <?php if (isset($course['fee'])): ?>
                                                        <span><i class="fas fa-rupee-sign"></i> <?php echo htmlspecialchars($course['fee']); ?></span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div class="course-description">
                                                <?php if (isset($course['description'])): ?>
                                                    <p><?php echo $course['description']; ?></p>
                                                <?php else: ?>
                                                    <p>No description available for this course.</p>
                                                <?php endif; ?>
                                            </div>
                                            <?php if (!empty($course['tags'])): ?>
                                                <div class="course-tags">
                                                    <?php foreach ($course['tags'] as $tag): ?>
                                                        <span class="badge bg-light text-dark"><?php echo htmlspecialchars($tag); ?></span>
                                                    <?php endforeach; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Success Stories Section -->
                        <?php if (!empty($successStories)): ?>
                        <div class="coaching-detail-card">
                            <h3>Success Stories</h3>
                            <div class="success-stories-list">
                                <?php foreach ($successStories as $story): ?>
                                    <div class="success-story-item">
                                        <div class="success-story-header">
                                            <div class="success-story-student">
                                                <?php if (!empty($story['student_photo'])): ?>
                                                    <img src="<?php echo getUploadUrl($story['student_photo']); ?>" alt="<?php echo htmlspecialchars($story['student_name']); ?>" class="student-photo">
                                                <?php else: ?>
                                                    <div class="student-photo-placeholder">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                <?php endif; ?>
                                                <div class="student-info">
                                                    <h4><?php echo htmlspecialchars($story['student_name']); ?></h4>
                                                    <?php if (!empty($story['achievement'])): ?>
                                                        <p class="achievement"><?php echo htmlspecialchars($story['achievement']); ?></p>
                                                    <?php endif; ?>
                                                    <?php if (!empty($story['year'])): ?>
                                                        <p class="year"><?php echo htmlspecialchars($story['year']); ?></p>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="success-story-content">
                                            <?php if (!empty($story['testimonial'])): ?>
                                                <p><?php echo $story['testimonial']; ?></p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Reviews Section -->
                        <?php if (!empty($reviews)): ?>
                        <div class="coaching-detail-card">
                            <h3>Student Reviews</h3>
                            <div class="reviews-list">
                                <?php foreach ($reviews as $review): ?>
                                    <div class="review-item">
                                        <div class="review-header">
                                            <div class="reviewer-info">
                                                <h4><?php echo htmlspecialchars($review['reviewer_name']); ?></h4>
                                                <div class="review-date">
                                                    <?php echo date('M d, Y', strtotime($review['review_date'])); ?>
                                                </div>
                                            </div>
                                            <div class="review-rating">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="fas fa-star <?php echo $i <= $review['rating'] ? 'filled' : ''; ?>"></i>
                                                <?php endfor; ?>
                                            </div>
                                        </div>
                                        <div class="review-content">
                                            <p><?php echo htmlspecialchars($review['review_text']); ?></p>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="col-lg-4">
                        <!-- Contact Information -->
                        <div class="coaching-sidebar-card">
                            <h3>Contact Information</h3>
                            <div class="contact-info">
                                <div class="contact-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <div>
                                        <h4>Address</h4>
                                        <?php if (isset($coaching['address'])): ?>
                                            <p><?php echo htmlspecialchars($coaching['address']); ?></p>
                                        <?php endif; ?>
                                        <?php if (isset($coaching['city_name']) && isset($coaching['state_name'])): ?>
                                            <p><?php echo htmlspecialchars($coaching['city_name']); ?>, <?php echo htmlspecialchars($coaching['state_name']); ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="contact-item">
                                    <i class="fas fa-phone"></i>
                                    <div>
                                        <h4>Phone</h4>
                                        <?php if (isset($coaching['phone'])): ?>
                                            <p><a href="tel:<?php echo htmlspecialchars($coaching['phone']); ?>"><?php echo htmlspecialchars($coaching['phone']); ?></a></p>
                                        <?php else: ?>
                                            <p>Not available</p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="contact-item">
                                    <i class="fas fa-envelope"></i>
                                    <div>
                                        <h4>Email</h4>
                                        <?php if (isset($coaching['email'])): ?>
                                            <p><a href="mailto:<?php echo htmlspecialchars($coaching['email']); ?>"><?php echo htmlspecialchars($coaching['email']); ?></a></p>
                                        <?php else: ?>
                                            <p>Not available</p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <?php if (!empty($coaching['website'])): ?>
                                <div class="contact-item">
                                    <i class="fas fa-globe"></i>
                                    <div>
                                        <h4>Website</h4>
                                        <p><a href="<?php echo htmlspecialchars($coaching['website']); ?>" target="_blank"><?php echo htmlspecialchars($coaching['website']); ?></a></p>
                                    </div>
                                </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($coaching['social_media'])): ?>
                                <div class="contact-item">
                                    <i class="fas fa-share-alt"></i>
                                    <div>
                                        <h4>Social Media</h4>
                                        <div class="social-links">
                                            <?php foreach ($coaching['social_media'] as $platform => $url): ?>
                                                <a href="<?php echo htmlspecialchars($url); ?>" target="_blank" class="social-link">
                                                    <i class="fab fa-<?php echo strtolower($platform); ?>"></i>
                                                </a>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Enquiry Form -->
                        <div class="coaching-sidebar-card" id="enquiry-form">
                            <h3>Enquire Now</h3>
                            
                            <form action="submit-enquiry.php" method="post" id="enquiryForm">
                                <input type="hidden" name="coaching_id" value="<?php echo $coaching['coaching_id']; ?>">
                                <!-- Hidden field for slug to ensure it's available on redirect -->
                                <input type="hidden" name="slug" value="<?php echo htmlspecialchars($coaching['slug']); ?>">
                                
                                <div class="mb-3">
                                    <label for="name" class="form-label">Your Name</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" required>
                                </div>
                                
                                <?php if (!empty($courses)): ?>
                                <div class="mb-3">
                                    <label for="course" class="form-label">Interested Course</label>
                                    <select class="form-select" id="course" name="course">
                                        <option value="">Select a course</option>
                                        <?php foreach ($courses as $course): ?>
                                            <option value="<?php echo $course['course_id']; ?>"><?php echo htmlspecialchars($course['course_name']); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($coaching['locations'])): ?>
                                <div class="mb-3">
                                    <label for="location_id" class="form-label">Preferred Location</label>
                                    <select class="form-select" id="location_id" name="location_id">
                                        <option value="">Select a location</option>
                                        <?php foreach ($coaching['locations'] as $location): ?>
                                            <option value="<?php echo $location['location_id']; ?>" <?php echo $locationFilter == $location['location_id'] ? 'selected' : ''; ?>><?php echo htmlspecialchars($location['location_name']); ?>, <?php echo htmlspecialchars($location['city_name']); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <?php endif; ?>
                                
                                <div class="mb-3">
                                    <label for="message" class="form-label">Message (Optional)</label>
                                    <textarea class="form-control" id="message" name="message" rows="3"></textarea>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100">Submit Enquiry</button>
                            </form>
                            
                            <?php if (isset($_GET['enquiry']) && $_GET['enquiry'] == 'success'): ?>
                                <div class="alert alert-success mt-3">
                                    Your enquiry has been submitted successfully. We will contact you soon.
                                </div>
                            <?php elseif (isset($_GET['enquiry']) && $_GET['enquiry'] == 'error'): ?>
                                <div class="alert alert-danger mt-3">
                                    <?php if (isset($_SESSION['enquiry_error'])): ?>
                                        <?php echo htmlspecialchars($_SESSION['enquiry_error']); ?>
                                        <?php unset($_SESSION['enquiry_error']); ?>
                                    <?php elseif (isset($_SESSION['enquiry_errors']) && is_array($_SESSION['enquiry_errors'])): ?>
                                        <ul class="mb-0">
                                            <?php foreach ($_SESSION['enquiry_errors'] as $error): ?>
                                                <li><?php echo htmlspecialchars($error); ?></li>
                                            <?php endforeach; ?>
                                        </ul>
                                        <?php unset($_SESSION['enquiry_errors']); ?>
                                    <?php else: ?>
                                        There was an error submitting your enquiry. Please try again.
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Map -->
                        <div class="coaching-sidebar-card">
                            <h3>Location Map</h3>
                            <div class="coaching-map">
                                <?php
                                // For a real implementation, you would store latitude and longitude for each location
                                // and generate a proper map URL. This is a placeholder.
                                $mapQuery = '';
                                if (isset($coaching['coaching_name']) && isset($coaching['city_name'])) {
                                    $address = isset($coaching['address']) ? $coaching['address'] : '';
                                    $locationName = '';
                                    if ($locationFilter && !empty($coaching['locations'])) {
                                        foreach ($coaching['locations'] as $loc) {
                                            if ($loc['location_id'] == $locationFilter) {
                                                $locationName = $loc['location_name'] . ', ' . $loc['city_name'];
                                                break;
                                            }
                                        }
                                    }
                                    $mapQuery = urlencode($coaching['coaching_name'] . ' ' . 
                                        ($locationFilter && !empty($locationName) ? $locationName : $address) . ' ' . 
                                        $coaching['city_name']);
                                }
                                ?>
                                <!-- Replace YOUR_API_KEY with an actual Google Maps API key in production -->
                                <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3505.0757837605!2d77.2272289!3d28.6129!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMjjCsDM2JzQ2LjQiTiA3N8KwMTMnMzguMCJF!5e0!3m2!1sen!2sin!4v1623825278428!5m2!1sen!2sin" 
                                        width="100%" height="300" style="border:0;" allowfullscreen="" loading="lazy"></iframe>
                                <!-- In a production environment, use: -->
                                <!-- <iframe src="https://www.google.com/maps/embed/v1/place?key=YOUR_API_KEY&q=<?php echo $mapQuery; ?>" width="100%" height="300" style="border:0;" allowfullscreen="" loading="lazy"></iframe> -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Similar Coaching Centers -->
        <section class="similar-coaching-section">
            <div class="container">
                <h2>Similar Coaching Centers</h2>
                
                <div class="row">
                    <?php 
                    // Get similar coaching centers
                    $similarCoachings = [];
                    if (isset($coaching['coaching_id'])) {
                        $similarCoachings = $coachingObj->getSimilar($coaching['coaching_id'], 3);
                    }
                    
                    if (empty($similarCoachings)): 
                    ?>
                        <div class="col-12">
                            <p class="text-center">No similar coaching centers found.</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($similarCoachings as $similarCoaching): ?>
                            <div class="col-md-4">
                                <div class="coaching-card">
                                    <div class="coaching-card-header">
                                        <img src="<?php echo isset($similarCoaching['logo']) ? getUploadUrl($similarCoaching['logo']) : getAssetUrl('images/default-logo.png'); ?>" alt="<?php echo htmlspecialchars($similarCoaching['coaching_name']); ?>" class="coaching-logo">
                                    </div>
                                    <div class="coaching-card-body">
                                        <h3><?php echo htmlspecialchars($similarCoaching['coaching_name']); ?></h3>
                                        <div class="coaching-meta">
                                            <span><i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($similarCoaching['city_name']); ?></span>
                                            <span><i class="fas fa-star"></i> <?php echo number_format($similarCoaching['avg_rating'], 1); ?></span>
                                        </div>
                                        <p class="coaching-excerpt"><?php echo substr(strip_tags($similarCoaching['description']), 0, 100) . '...'; ?></p>
                                        <a href="center.php?slug=<?php echo urlencode($similarCoaching['slug']); ?>" class="btn btn-outline-primary btn-sm">View Details</a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </section>
    </main>
    
    <!-- Footer -->
    <?php include 'templates/footer.php'; ?>
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo getAssetUrl('js/main.js'); ?>"></script>
</body>
</html>