<?php
/**
 * CoachingCenter Class
 * Handles coaching center operations
 */
class CoachingCenter {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Get coaching center by user ID
     * @param int $userId User ID
     * @return array|null Coaching center data
     */
    public function getByUserId($userId) {
        return $this->db->fetchRow(
            "SELECT c.*, ci.city_name, s.state_name, l.location_name
             FROM coaching_centers c
             LEFT JOIN cities ci ON c.city_id = ci.city_id
             LEFT JOIN states s ON c.state_id = s.state_id
             LEFT JOIN locations l ON c.location_id = l.location_id
             WHERE c.user_id = ?",
            [$userId]
        );
    }
    
    /**
     * Get coaching center ID by user ID
     * @param int $userId User ID
     * @return int|null Coaching center ID
     */
    public function getCoachingIdByUserId($userId) {
        $result = $this->db->fetchOne(
            "SELECT coaching_id FROM coaching_centers WHERE user_id = ?",
            [$userId]
        );
        
        return $result ? $result['coaching_id'] : null;
    }
    
    /**
     * Get basic coaching center data by slug (without additional data)
     * @param string $slug Coaching center slug
     * @return array|null Basic coaching center data
     */
    public function getBasicBySlug($slug) {
        return $this->db->fetchRow(
            "SELECT c.*, ci.city_name, s.state_name, l.location_name
             FROM coaching_centers c
             LEFT JOIN cities ci ON c.city_id = ci.city_id
             LEFT JOIN states s ON c.state_id = s.state_id
             LEFT JOIN locations l ON c.location_id = l.location_id
             WHERE c.slug = ?",
            [$slug]
        );
    }
    
    /**
     * Get categories for a coaching center
     * @param int $coachingId Coaching center ID
     * @return array Categories
     */
    public function getCategories($coachingId) {
        return $this->db->fetchAll(
            "SELECT cc.*, c.category_name, c.slug as category_slug
             FROM coaching_categories cc
             JOIN course_categories c ON cc.category_id = c.category_id
             WHERE cc.coaching_id = ?",
            [$coachingId]
        );
    }
    
    /**
     * Update categories for a coaching center
     * @param int $coachingId Coaching center ID
     * @param array $categoryIds Category IDs
     * @return bool True if update successful
     */
    public function updateCategories($coachingId, $categoryIds) {
        // Start transaction
        $this->db->beginTransaction();
        
        try {
            // Delete existing categories
            $this->db->delete('coaching_categories', 'coaching_id = ?', [$coachingId]);
            
            // Insert new categories
            foreach ($categoryIds as $categoryId) {
                $this->db->insert('coaching_categories', [
                    'coaching_id' => $coachingId,
                    'category_id' => $categoryId
                ]);
            }
            
            // Commit transaction
            $this->db->commit();
            
            return true;
        } catch (Exception $e) {
            // Rollback transaction
            $this->db->rollback();
            return false;
        }
    }
    
    /**
     * Get facilities with coaching_facilities data for a coaching center
     * @param int $coachingId Coaching center ID
     * @return array Facilities with coaching_facilities data
     */
    public function getFacilitiesWithJoinData($coachingId) {
        return $this->db->fetchAll(
            "SELECT cf.*, f.facility_name, f.icon
             FROM coaching_facilities cf
             JOIN facilities f ON cf.facility_id = f.facility_id
             WHERE cf.coaching_id = ?",
            [$coachingId]
        );
    }
    
    /**
     * Update facilities for a coaching center
     * @param int $coachingId Coaching center ID
     * @param array $facilityIds Facility IDs
     * @return bool True if update successful
     */
    public function updateFacilities($coachingId, $facilityIds) {
        // Start transaction
        $this->db->beginTransaction();
        
        try {
            // Delete existing facilities
            $this->db->delete('coaching_facilities', 'coaching_id = ?', [$coachingId]);
            
            // Insert new facilities
            foreach ($facilityIds as $facilityId) {
                $this->db->insert('coaching_facilities', [
                    'coaching_id' => $coachingId,
                    'facility_id' => $facilityId
                ]);
            }
            
            // Commit transaction
            $this->db->commit();
            
            return true;
        } catch (Exception $e) {
            // Rollback transaction
            $this->db->rollback();
            return false;
        }
    }
    
    /**
     * Get inquiry count for a coaching center
     * @param int $coachingId Coaching center ID
     * @return int Inquiry count
     */
    public function getInquiryCount($coachingId) {
        $result = $this->db->fetchRow(
            "SELECT COUNT(*) as count FROM inquiries WHERE coaching_id = ?",
            [$coachingId]
        );
        
        return $result['count'] ?? 0;
    }
    
    /**
     * Get course count for a coaching center
     * @param int $coachingId Coaching center ID
     * @return int Course count
     */
    public function getCourseCount($coachingId) {
        $result = $this->db->fetchRow(
            "SELECT COUNT(*) as count FROM courses WHERE coaching_id = ?",
            [$coachingId]
        );
        
        return $result['count'] ?? 0;
    }
    
    /**
     * Get success story count for a coaching center
     * @param int $coachingId Coaching center ID
     * @return int Success story count
     */
    public function getSuccessStoryCount($coachingId) {
        $result = $this->db->fetchRow(
            "SELECT COUNT(*) as count FROM success_stories WHERE coaching_id = ?",
            [$coachingId]
        );
        
        return $result['count'] ?? 0;
    }
    
    /**
     * Create a new coaching center
     * @param array $data Coaching center data
     * @return int|bool Coaching center ID on success, false on failure
     */
    public function create($data) {
        // Generate slug from coaching name
        $data['slug'] = Utility::generateUniqueSlug($data['coaching_name'], 'coaching_centers', 'slug');
        
        // Begin transaction
        $this->db->beginTransaction();
        
        try {
            // Insert coaching center
            $coachingId = $this->db->insert('coaching_centers', $data);
            
            if (!$coachingId) {
                $this->db->rollback();
                return false;
            }
            
            // Insert categories if provided
            if (isset($data['categories']) && is_array($data['categories'])) {
                foreach ($data['categories'] as $categoryId) {
                    $this->db->insert('coaching_categories', [
                        'coaching_id' => $coachingId,
                        'category_id' => $categoryId
                    ]);
                }
            }
            
            // Insert facilities if provided
            if (isset($data['facilities']) && is_array($data['facilities'])) {
                foreach ($data['facilities'] as $facilityId) {
                    $this->db->insert('coaching_facilities', [
                        'coaching_id' => $coachingId,
                        'facility_id' => $facilityId
                    ]);
                }
            }
            
            // Commit transaction
            $this->db->commit();
            
            return $coachingId;
        } catch (Exception $e) {
            $this->db->rollback();
            return false;
        }
    }
    
    /**
     * Update a coaching center
     * @param int $coachingId Coaching center ID
     * @param array $data Coaching center data
     * @return bool True if update successful
     */
    public function update($coachingId, $data) {
        error_log("Updating coaching center ID: " . $coachingId);
        error_log("Update data: " . print_r($data, true));
        
        // Check if coaching name is being changed
        if (isset($data['coaching_name'])) {
            $currentCoaching = $this->getById($coachingId);
            error_log("Current coaching data: " . print_r($currentCoaching, true));
            
            if ($currentCoaching && $data['coaching_name'] !== $currentCoaching['coaching_name']) {
                error_log("Coaching name changed, generating new slug");
                $data['slug'] = Utility::generateUniqueSlug($data['coaching_name'], 'coaching_centers', 'slug', $coachingId);
                error_log("New slug: " . $data['slug']);
            } else if ($currentCoaching) {
                // Make sure we always include the slug in the update data
                $data['slug'] = $currentCoaching['slug'];
                error_log("Using existing slug: " . $data['slug']);
            }
        }
        
        // Begin transaction
        $this->db->beginTransaction();
        
        try {
            // Update coaching center
            error_log("Executing update query for coaching_centers table");
            $updated = $this->db->update('coaching_centers', $data, 'coaching_id = ?', [$coachingId]);
            
            if (!$updated) {
                $error = $this->db->getLastError();
                error_log("Update failed: " . $error);
                $this->db->rollback();
                return false;
            }
            
            // Update categories if provided
            if (isset($data['categories']) && is_array($data['categories'])) {
                error_log("Updating categories for coaching center");
                // Delete existing categories
                $deleted = $this->db->delete('coaching_categories', 'coaching_id = ?', [$coachingId]);
                error_log("Deleted existing categories: " . ($deleted ? "Yes" : "No"));
                
                // Insert new categories
                foreach ($data['categories'] as $categoryId) {
                    $inserted = $this->db->insert('coaching_categories', [
                        'coaching_id' => $coachingId,
                        'category_id' => $categoryId
                    ]);
                    error_log("Inserted category ID {$categoryId}: " . ($inserted ? "Yes" : "No"));
                    if (!$inserted) {
                        error_log("Failed to insert category: " . $this->db->getLastError());
                    }
                }
            }
            
            // Update facilities if provided
            if (isset($data['facilities']) && is_array($data['facilities'])) {
                error_log("Updating facilities for coaching center");
                // Delete existing facilities
                $deleted = $this->db->delete('coaching_facilities', 'coaching_id = ?', [$coachingId]);
                error_log("Deleted existing facilities: " . ($deleted ? "Yes" : "No"));
                
                // Insert new facilities
                foreach ($data['facilities'] as $facilityId) {
                    $inserted = $this->db->insert('coaching_facilities', [
                        'coaching_id' => $coachingId,
                        'facility_id' => $facilityId
                    ]);
                    error_log("Inserted facility ID {$facilityId}: " . ($inserted ? "Yes" : "No"));
                    if (!$inserted) {
                        error_log("Failed to insert facility: " . $this->db->getLastError());
                    }
                }
            }
            
            // Commit transaction
            error_log("Committing transaction");
            $this->db->commit();
            
            error_log("Update completed successfully");
            return true;
        } catch (Exception $e) {
            error_log("Exception during update: " . $e->getMessage());
            $this->db->rollback();
            return false;
        }
    }
    
    /**
     * Delete a coaching center
     * @param int $coachingId Coaching center ID
     * @return bool True if deletion successful
     */
    public function delete($coachingId) {
        // Begin transaction
        $this->db->beginTransaction();
        
        try {
            // Delete coaching center images
            $images = $this->db->fetchAll(
                "SELECT image_path FROM coaching_images WHERE coaching_id = ?",
                [$coachingId]
            );
            
            foreach ($images as $image) {
                Utility::deleteFile(BASE_PATH . '/' . $image['image_path']);
            }
            
            // Delete related records
            $this->db->delete('coaching_categories', 'coaching_id = ?', [$coachingId]);
            $this->db->delete('coaching_facilities', 'coaching_id = ?', [$coachingId]);
            $this->db->delete('coaching_images', 'coaching_id = ?', [$coachingId]);
            $this->db->delete('courses', 'coaching_id = ?', [$coachingId]);
            $this->db->delete('success_stories', 'coaching_id = ?', [$coachingId]);
            $this->db->delete('reviews', 'coaching_id = ?', [$coachingId]);
            $this->db->delete('inquiries', 'coaching_id = ?', [$coachingId]);
            $this->db->delete('user_favorites', 'coaching_id = ?', [$coachingId]);
            $this->db->delete('coaching_subscriptions', 'coaching_id = ?', [$coachingId]);
            
            // Delete coaching center
            $deleted = $this->db->delete('coaching_centers', 'coaching_id = ?', [$coachingId]);
            
            if (!$deleted) {
                $this->db->rollback();
                return false;
            }
            
            // Commit transaction
            $this->db->commit();
            
            return true;
        } catch (Exception $e) {
            $this->db->rollback();
            return false;
        }
    }
    
    /**
     * Get all coaching centers for dropdown
     * @param string $status Status filter (optional)
     * @return array Coaching centers
     */
    public function getAllForDropdown($status = 'active') {
        $sql = "SELECT coaching_id, coaching_name FROM coaching_centers";
        $params = [];
        
        if (!empty($status)) {
            $sql .= " WHERE status = ?";
            $params[] = $status;
        }
        
        $sql .= " ORDER BY coaching_name ASC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get count by status
     * @param string $status Status
     * @return int Count
     */
    public function getCountByStatus($status) {
        return $this->db->count('coaching_centers', 'status = ?', [$status]);
    }
    
    /**
     * Get coaching center by email
     * @param string $email Email address
     * @return array|null Coaching center data
     */
    public function getByEmail($email) {
        return $this->db->fetchRow(
            "SELECT * FROM coaching_centers WHERE email = ?",
            [$email]
        );
    }
    
    /**
     * Get all locations for a coaching center
     * @param int $coachingId Coaching center ID
     * @return array Locations
     */
    public function getLocations($coachingId) {
        return $this->db->fetchAll(
            "SELECT l.*, c.city_name, s.state_name 
             FROM coaching_locations cl
             JOIN locations l ON cl.location_id = l.location_id
             LEFT JOIN cities c ON l.city_id = c.city_id
             LEFT JOIN states s ON c.state_id = s.state_id
             WHERE cl.coaching_id = ?
             ORDER BY l.location_name ASC",
            [$coachingId]
        );
    }
    
    /**
     * Update locations for a coaching center
     * @param int $coachingId Coaching center ID
     * @param array $locationIds Location IDs
     * @return bool True if update successful
     */
    public function updateLocations($coachingId, $locationIds) {
        // Start transaction
        $this->db->beginTransaction();
        
        try {
            // Delete existing locations
            $this->db->delete('coaching_locations', 'coaching_id = ?', [$coachingId]);
            
            // Insert new locations
            foreach ($locationIds as $locationId) {
                $this->db->insert('coaching_locations', [
                    'coaching_id' => $coachingId,
                    'location_id' => $locationId
                ]);
            }
            
            // Commit transaction
            $this->db->commit();
            
            return true;
        } catch (Exception $e) {
            // Rollback transaction
            $this->db->rollback();
            return false;
        }
    }
    
    /**
     * Get similar coaching centers
     * @param int $coachingId Current coaching center ID
     * @param int $limit Number of similar coaching centers to return
     * @return array Similar coaching centers
     */
    public function getSimilar($coachingId, $limit = 3) {
        // Get current coaching center's categories
        $categories = $this->db->fetchAll(
            "SELECT category_id FROM coaching_categories WHERE coaching_id = ?",
            [$coachingId]
        );
        
        if (empty($categories)) {
            // If no categories, return random coaching centers
            return $this->db->fetchAll(
                "SELECT c.coaching_id, c.coaching_name, c.slug, c.logo, c.address, 
                        ci.city_name, s.state_name
                 FROM coaching_centers c
                 LEFT JOIN cities ci ON c.city_id = ci.city_id
                 LEFT JOIN states s ON c.state_id = s.state_id
                 WHERE c.coaching_id != ? AND c.status = 'active'
                 ORDER BY RAND()
                 LIMIT ?",
                [$coachingId, $limit]
            );
        }
        
        // Extract category IDs
        $categoryIds = array_column($categories, 'category_id');
        
        // Get coaching centers with similar categories
        $placeholders = implode(',', array_fill(0, count($categoryIds), '?'));
        
        return $this->db->fetchAll(
            "SELECT c.coaching_id, c.coaching_name, c.slug, c.logo, c.address, 
                    ci.city_name, s.state_name, COUNT(cc.category_id) as common_categories
             FROM coaching_centers c
             JOIN coaching_categories cc ON c.coaching_id = cc.coaching_id
             LEFT JOIN cities ci ON c.city_id = ci.city_id
             LEFT JOIN states s ON c.state_id = s.state_id
             WHERE c.coaching_id != ? AND c.status = 'active' 
             AND cc.category_id IN ({$placeholders})
             GROUP BY c.coaching_id
             ORDER BY common_categories DESC, c.is_featured DESC, c.coaching_name ASC
             LIMIT ?",
            array_merge([$coachingId], $categoryIds, [$limit])
        );
    }
    

    
    /**
     * Get coaching center by ID
     * @param int $coachingId Coaching center ID
     * @return array|null Coaching center data
     */
    public function getById($coachingId) {
        $coaching = $this->db->fetchRow(
            "SELECT c.*, 
                    ci.city_name, 
                    s.state_name,
                    u.username, u.email as user_email
             FROM coaching_centers c
             LEFT JOIN cities ci ON c.city_id = ci.city_id
             LEFT JOIN states s ON c.state_id = s.state_id
             LEFT JOIN users u ON c.user_id = u.user_id
             WHERE c.coaching_id = ?",
            [$coachingId]
        );
        
        if (!$coaching) {
            return null;
        }
        
        // Get all locations for this coaching center
        $coaching['locations'] = $this->getLocations($coaching['coaching_id']);
        
        // Get categories
        $coaching['categories'] = $this->db->fetchAll(
            "SELECT cc.category_id, cat.category_name, cat.slug as category_slug
             FROM coaching_categories cc
             JOIN course_categories cat ON cc.category_id = cat.category_id
             WHERE cc.coaching_id = ?",
            [$coachingId]
        );
        
        // Get facilities
        $coaching['facilities'] = $this->db->fetchAll(
            "SELECT cf.facility_id, f.facility_name, f.icon
             FROM coaching_facilities cf
             JOIN facilities f ON cf.facility_id = f.facility_id
             WHERE cf.coaching_id = ?",
            [$coachingId]
        );
        
        // Get images
        $coaching['images'] = $this->db->fetchAll(
            "SELECT * FROM coaching_images 
             WHERE coaching_id = ? 
             ORDER BY display_order ASC",
            [$coachingId]
        );
        
        return $coaching;
    }
    
    /**
     * Get coaching center by slug
     * @param string $slug Coaching center slug
     * @return array|null Coaching center data
     */
    public function getBySlug($slug) {
        error_log("Attempting to get coaching center by slug: " . $slug);
        error_log("Database status: " . ($this->db ? "Connected" : "Not connected"));
        
        $sql = "SELECT c.*, ci.city_name, s.state_name
                FROM coaching_centers c
                LEFT JOIN cities ci ON c.city_id = ci.city_id
                LEFT JOIN states s ON c.state_id = s.state_id
                WHERE c.slug = ? AND c.status = 'active'";
        error_log("Executing query: " . $sql);
        
        $coaching = $this->db->fetchRow(
            $sql,
            [$slug]
        );
        
        if (!$coaching) {
            return null;
        }
        
        // Increment view count
        $this->db->update(
            'coaching_centers',
            ['total_views' => $coaching['total_views'] + 1],
            'coaching_id = ?',
            [$coaching['coaching_id']]
        );
        
        // Get all locations for this coaching center
        $coaching['locations'] = $this->getLocations($coaching['coaching_id']);
        
        // Get categories
        $coaching['categories'] = $this->db->fetchAll(
            "SELECT cc.category_id, cat.category_name, cat.slug as category_slug
             FROM coaching_categories cc
             JOIN course_categories cat ON cc.category_id = cat.category_id
             WHERE cc.coaching_id = ?",
            [$coaching['coaching_id']]
        );
        
        // Get facilities
        $coaching['facilities'] = $this->db->fetchAll(
            "SELECT cf.facility_id, f.facility_name, f.icon
             FROM coaching_facilities cf
             JOIN facilities f ON cf.facility_id = f.facility_id
             WHERE cf.coaching_id = ?",
            [$coaching['coaching_id']]
        );
        
        // Get images
        $coaching['images'] = $this->db->fetchAll(
            "SELECT * FROM coaching_images 
             WHERE coaching_id = ? 
             ORDER BY display_order ASC",
            [$coaching['coaching_id']]
        );
        
        // Get courses
        $coaching['courses'] = $this->db->fetchAll(
            "SELECT c.*, cc.category_name
             FROM courses c
             LEFT JOIN course_categories cc ON c.category_id = cc.category_id
             WHERE c.coaching_id = ? AND c.status = 'active'
             ORDER BY c.course_name ASC",
            [$coaching['coaching_id']]
        );
        
        // Get success stories
        $coaching['success_stories'] = $this->db->fetchAll(
            "SELECT ss.*, c.course_name
             FROM success_stories ss
             LEFT JOIN courses c ON ss.course_id = c.course_id
             WHERE ss.coaching_id = ? AND ss.status = 'active'
             ORDER BY ss.is_featured DESC, ss.year DESC, ss.display_order ASC
             LIMIT 10",
            [$coaching['coaching_id']]
        );
        
        // Get reviews
        $coaching['reviews'] = $this->db->fetchAll(
            "SELECT r.*, u.username, u.first_name, u.last_name, u.profile_image
             FROM reviews r
             LEFT JOIN users u ON r.user_id = u.user_id
             WHERE r.coaching_id = ? AND r.status = 'approved'
             ORDER BY r.created_at DESC
             LIMIT 5",
            [$coaching['coaching_id']]
        );
        
        return $coaching;
    }
    
    /**
     * Search coaching centers
     * @param array $filters Search filters
     * @param int $page Page number
     * @param int $limit Records per page
     * @return array Coaching centers and pagination info
     */
    public function search($filters = [], $page = 1, $limit = RECORDS_PER_PAGE) {
        $where = "c.status = 'active'";
        $params = [];
        
        // Apply filters
        if (isset($filters['category_id']) && !empty($filters['category_id'])) {
            $where .= " AND c.coaching_id IN (SELECT coaching_id FROM coaching_categories WHERE category_id = ?)";
            $params[] = $filters['category_id'];
        }
        
        if (isset($filters['city_id']) && !empty($filters['city_id'])) {
            $where .= " AND c.city_id = ?";
            $params[] = $filters['city_id'];
        }
        
        if (isset($filters['state_id']) && !empty($filters['state_id'])) {
            $where .= " AND c.state_id = ?";
            $params[] = $filters['state_id'];
        }
        
        if (isset($filters['location_id']) && !empty($filters['location_id'])) {
            $where .= " AND c.location_id = ?";
            $params[] = $filters['location_id'];
        }
        
        if (isset($filters['keyword']) && !empty($filters['keyword'])) {
            $where .= " AND (c.coaching_name LIKE ? OR c.description LIKE ?)";
            $keyword = '%' . $filters['keyword'] . '%';
            $params[] = $keyword;
            $params[] = $keyword;
        }
        
        // Get total count
        $countSql = "SELECT COUNT(DISTINCT c.coaching_id) as count
                     FROM coaching_centers c
                     WHERE {$where}";
        $countResult = $this->db->fetchRow($countSql, $params);
        $totalCount = $countResult['count'];
        
        // Calculate pagination
        $totalPages = ceil($totalCount / $limit);
        $offset = ($page - 1) * $limit;
        
        // Get coaching centers
        $sql = "SELECT c.*, 
                       ci.city_name, 
                       s.state_name,
                       (SELECT COUNT(*) FROM reviews r WHERE r.coaching_id = c.coaching_id AND r.status = 'approved') as total_reviews,
                       (SELECT AVG(rating) FROM reviews r WHERE r.coaching_id = c.coaching_id AND r.status = 'approved') as avg_rating
                FROM coaching_centers c
                LEFT JOIN cities ci ON c.city_id = ci.city_id
                LEFT JOIN states s ON c.state_id = s.state_id
                WHERE {$where}
                GROUP BY c.coaching_id
                ORDER BY c.is_featured DESC, c.total_views DESC
                LIMIT {$offset}, {$limit}";
        
        $coachings = $this->db->fetchAll($sql, $params);
        
        // Get categories for each coaching center
        foreach ($coachings as &$coaching) {
            $coaching['categories'] = $this->db->fetchAll(
                "SELECT cc.category_id, cat.category_name, cat.slug as category_slug
                 FROM coaching_categories cc
                 JOIN course_categories cat ON cc.category_id = cat.category_id
                 WHERE cc.coaching_id = ?",
                [$coaching['coaching_id']]
            );
        }
        
        return [
            'coachings' => $coachings,
            'pagination' => [
                'total_count' => $totalCount,
                'total_pages' => $totalPages,
                'current_page' => $page,
                'limit' => $limit
            ]
        ];
    }
    
    /**
     * Get featured coaching centers
     * @param int $limit Number of coaching centers to get
     * @return array Featured coaching centers
     */
    public function getFeatured($limit = 6) {
        $sql = "SELECT c.*, 
                       ci.city_name, 
                       s.state_name,
                       (SELECT COUNT(*) FROM reviews r WHERE r.coaching_id = c.coaching_id AND r.status = 'approved') as total_reviews,
                       (SELECT AVG(rating) FROM reviews r WHERE r.coaching_id = c.coaching_id AND r.status = 'approved') as avg_rating
                FROM coaching_centers c
                LEFT JOIN cities ci ON c.city_id = ci.city_id
                LEFT JOIN states s ON c.state_id = s.state_id
                WHERE c.status = 'active' AND c.is_featured = 1
                ORDER BY c.total_views DESC
                LIMIT ?";
        
        $coachings = $this->db->fetchAll($sql, [$limit]);
        
        // Get categories for each coaching center
        foreach ($coachings as &$coaching) {
            $coaching['categories'] = $this->db->fetchAll(
                "SELECT cc.category_id, cat.category_name, cat.slug as category_slug
                 FROM coaching_categories cc
                 JOIN course_categories cat ON cc.category_id = cat.category_id
                 WHERE cc.coaching_id = ?",
                [$coaching['coaching_id']]
            );
        }
        
        return $coachings;
    }
    
    /**
     * Get top rated coaching centers
     * @param int $limit Number of coaching centers to get
     * @return array Top rated coaching centers
     */
    public function getTopRated($limit = 6) {
        $sql = "SELECT c.*, 
                       ci.city_name, 
                       s.state_name,
                       (SELECT COUNT(*) FROM reviews r WHERE r.coaching_id = c.coaching_id AND r.status = 'approved') as total_reviews,
                       (SELECT AVG(rating) FROM reviews r WHERE r.coaching_id = c.coaching_id AND r.status = 'approved') as avg_rating
                FROM coaching_centers c
                LEFT JOIN cities ci ON c.city_id = ci.city_id
                LEFT JOIN states s ON c.state_id = s.state_id
                WHERE c.status = 'active'
                HAVING avg_rating IS NOT NULL
                ORDER BY avg_rating DESC, total_reviews DESC, c.is_featured DESC
                LIMIT ?";
        
        $coachings = $this->db->fetchAll($sql, [$limit]);
        
        // Get categories for each coaching center
        foreach ($coachings as &$coaching) {
            $coaching['categories'] = $this->db->fetchAll(
                "SELECT cc.category_id, cat.category_name, cat.slug as category_slug
                 FROM coaching_categories cc
                 JOIN course_categories cat ON cc.category_id = cat.category_id
                 WHERE cc.coaching_id = ?",
                [$coaching['coaching_id']]
            );
        }
        
        return $coachings;
    }
    
    /**
     * Get popular coaching centers
     * @param int $limit Number of coaching centers to get
     * @return array Popular coaching centers
     */
    public function getPopular($limit = 6) {
        $sql = "SELECT c.*, 
                       ci.city_name, 
                       s.state_name,
                       (SELECT COUNT(*) FROM reviews r WHERE r.coaching_id = c.coaching_id AND r.status = 'approved') as total_reviews,
                       (SELECT AVG(rating) FROM reviews r WHERE r.coaching_id = c.coaching_id AND r.status = 'approved') as avg_rating
                FROM coaching_centers c
                LEFT JOIN cities ci ON c.city_id = ci.city_id
                LEFT JOIN states s ON c.state_id = s.state_id
                WHERE c.status = 'active'
                ORDER BY c.total_views DESC, c.is_featured DESC
                LIMIT ?";
        
        $coachings = $this->db->fetchAll($sql, [$limit]);
        
        // Get categories for each coaching center
        foreach ($coachings as &$coaching) {
            $coaching['categories'] = $this->db->fetchAll(
                "SELECT cc.category_id, cat.category_name, cat.slug as category_slug
                 FROM coaching_categories cc
                 JOIN course_categories cat ON cc.category_id = cat.category_id
                 WHERE cc.coaching_id = ?",
                [$coaching['coaching_id']]
            );
        }
        
        return $coachings;
    }
    
    /**
     * Get coaching centers by category
     * @param int $categoryId Category ID
     * @param int $page Page number
     * @param int $limit Records per page
     * @return array Coaching centers and pagination info
     */
    public function getByCategory($categoryId, $page = 1, $limit = RECORDS_PER_PAGE) {
        // Get total count
        $countSql = "SELECT COUNT(DISTINCT c.coaching_id) as count
                     FROM coaching_centers c
                     JOIN coaching_categories cc ON c.coaching_id = cc.coaching_id
                     WHERE c.status = 'active' AND cc.category_id = ?";
        $countResult = $this->db->fetchRow($countSql, [$categoryId]);
        $totalCount = $countResult['count'];
        
        // Calculate pagination
        $totalPages = ceil($totalCount / $limit);
        $offset = ($page - 1) * $limit;
        
        // Get coaching centers
        $sql = "SELECT c.*, 
                       ci.city_name, 
                       s.state_name,
                       (SELECT COUNT(*) FROM reviews r WHERE r.coaching_id = c.coaching_id AND r.status = 'approved') as total_reviews,
                       (SELECT AVG(rating) FROM reviews r WHERE r.coaching_id = c.coaching_id AND r.status = 'approved') as avg_rating
                FROM coaching_centers c
                JOIN coaching_categories cc ON c.coaching_id = cc.coaching_id
                LEFT JOIN cities ci ON c.city_id = ci.city_id
                LEFT JOIN states s ON c.state_id = s.state_id
                WHERE c.status = 'active' AND cc.category_id = ?
                GROUP BY c.coaching_id
                ORDER BY c.is_featured DESC, c.total_views DESC
                LIMIT {$offset}, {$limit}";
        
        $coachings = $this->db->fetchAll($sql, [$categoryId]);
        
        // Get categories for each coaching center
        foreach ($coachings as &$coaching) {
            $coaching['categories'] = $this->db->fetchAll(
                "SELECT cc.category_id, cat.category_name, cat.slug as category_slug
                 FROM coaching_categories cc
                 JOIN course_categories cat ON cc.category_id = cat.category_id
                 WHERE cc.coaching_id = ?",
                [$coaching['coaching_id']]
            );
        }
        
        return [
            'coachings' => $coachings,
            'pagination' => [
                'total_count' => $totalCount,
                'total_pages' => $totalPages,
                'current_page' => $page,
                'limit' => $limit
            ]
        ];
    }
    
    /**
     * Get coaching centers by location
     * @param int $locationId Location ID
     * @param int $page Page number
     * @param int $limit Records per page
     * @return array Coaching centers and pagination info
     */
    public function getByLocation($locationId, $page = 1, $limit = RECORDS_PER_PAGE) {
        // Get total count
        $countSql = "SELECT COUNT(*) as count
                     FROM coaching_centers c
                     WHERE c.status = 'active' AND c.location_id = ?";
        $countResult = $this->db->fetchRow($countSql, [$locationId]);
        $totalCount = $countResult['count'];
        
        // Calculate pagination
        $totalPages = ceil($totalCount / $limit);
        $offset = ($page - 1) * $limit;
        
        // Get coaching centers
        $sql = "SELECT c.*, 
                       ci.city_name, 
                       s.state_name,
                       (SELECT COUNT(*) FROM reviews r WHERE r.coaching_id = c.coaching_id AND r.status = 'approved') as total_reviews,
                       (SELECT AVG(rating) FROM reviews r WHERE r.coaching_id = c.coaching_id AND r.status = 'approved') as avg_rating
                FROM coaching_centers c
                LEFT JOIN cities ci ON c.city_id = ci.city_id
                LEFT JOIN states s ON c.state_id = s.state_id
                WHERE c.status = 'active' AND c.location_id = ?
                ORDER BY c.is_featured DESC, c.total_views DESC
                LIMIT {$offset}, {$limit}";
        
        $coachings = $this->db->fetchAll($sql, [$locationId]);
        
        // Get categories for each coaching center
        foreach ($coachings as &$coaching) {
            $coaching['categories'] = $this->db->fetchAll(
                "SELECT cc.category_id, cat.category_name, cat.slug as category_slug
                 FROM coaching_categories cc
                 JOIN course_categories cat ON cc.category_id = cat.category_id
                 WHERE cc.coaching_id = ?",
                [$coaching['coaching_id']]
            );
        }
        
        return [
            'coachings' => $coachings,
            'pagination' => [
                'total_count' => $totalCount,
                'total_pages' => $totalPages,
                'current_page' => $page,
                'limit' => $limit
            ]
        ];
    }
    
    /**
     * Get coaching centers by city
     * @param int $cityId City ID
     * @param int $page Page number
     * @param int $limit Records per page
     * @return array Coaching centers and pagination info
     */
    public function getByCity($cityId, $page = 1, $limit = RECORDS_PER_PAGE) {
        // Get total count
        $countSql = "SELECT COUNT(*) as count
                     FROM coaching_centers c
                     WHERE c.status = 'active' AND c.city_id = ?";
        $countResult = $this->db->fetchRow($countSql, [$cityId]);
        $totalCount = $countResult['count'];
        
        // Calculate pagination
        $totalPages = ceil($totalCount / $limit);
        $offset = ($page - 1) * $limit;
        
        // Get coaching centers
        $sql = "SELECT c.*, 
                       ci.city_name, 
                       s.state_name,
                       (SELECT COUNT(*) FROM reviews r WHERE r.coaching_id = c.coaching_id AND r.status = 'approved') as total_reviews,
                       (SELECT AVG(rating) FROM reviews r WHERE r.coaching_id = c.coaching_id AND r.status = 'approved') as avg_rating
                FROM coaching_centers c
                LEFT JOIN cities ci ON c.city_id = ci.city_id
                LEFT JOIN states s ON c.state_id = s.state_id
                WHERE c.status = 'active' AND c.city_id = ?
                ORDER BY c.is_featured DESC, c.total_views DESC
                LIMIT {$offset}, {$limit}";
        
        $coachings = $this->db->fetchAll($sql, [$cityId]);
        
        // Get categories for each coaching center
        foreach ($coachings as &$coaching) {
            $coaching['categories'] = $this->db->fetchAll(
                "SELECT cc.category_id, cat.category_name, cat.slug as category_slug
                 FROM coaching_categories cc
                 JOIN course_categories cat ON cc.category_id = cat.category_id
                 WHERE cc.coaching_id = ?",
                [$coaching['coaching_id']]
            );
        }
        
        return [
            'coachings' => $coachings,
            'pagination' => [
                'total_count' => $totalCount,
                'total_pages' => $totalPages,
                'current_page' => $page,
                'limit' => $limit
            ]
        ];
    }
    
    /**
     * Get coaching centers by user
     * @param int $userId User ID
     * @return array Coaching centers
     */
    public function getByUser($userId) {
        $sql = "SELECT c.*, 
                       ci.city_name, 
                       s.state_name
                FROM coaching_centers c
                LEFT JOIN cities ci ON c.city_id = ci.city_id
                LEFT JOIN states s ON c.state_id = s.state_id
                WHERE c.user_id = ?
                ORDER BY c.coaching_name ASC";
        
        return $this->db->fetchAll($sql, [$userId]);
    }
    
    /**
     * Get courses offered by a coaching center
     * @param int $coachingId Coaching center ID
     * @param int $locationId Optional location ID to filter courses by location
     * @return array Courses
     */
    public function getCourses($coachingId, $locationId = null) {
        if ($locationId) {
            // Get courses available at a specific location
            return $this->db->fetchAll(
                "SELECT c.*, cc.category_name
                 FROM courses c
                 LEFT JOIN course_categories cc ON c.category_id = cc.category_id
                 JOIN course_locations cl ON c.course_id = cl.course_id
                 WHERE c.coaching_id = ? AND cl.location_id = ? AND c.status = 'active'
                 ORDER BY c.course_name ASC",
                [$coachingId, $locationId]
            );
        } else {
            // Get all courses for the coaching center
            return $this->db->fetchAll(
                "SELECT c.*, cc.category_name
                 FROM courses c
                 LEFT JOIN course_categories cc ON c.category_id = cc.category_id
                 WHERE c.coaching_id = ? AND c.status = 'active'
                 ORDER BY c.course_name ASC",
                [$coachingId]
            );
        }
    }
    
    /**
     * Get unique courses offered by a coaching center
     * This prevents duplicate courses that are offered at multiple locations
     * @param int $coachingId Coaching center ID
     * @return array Unique courses
     */
    public function getUniqueCourses($coachingId) {
        // Get all courses for the coaching center without duplicates
        return $this->db->fetchAll(
            "SELECT DISTINCT c.*, cc.category_name
             FROM courses c
             LEFT JOIN course_categories cc ON c.category_id = cc.category_id
             WHERE c.coaching_id = ? AND c.status = 'active'
             ORDER BY c.course_name ASC",
            [$coachingId]
        );
    }
    
    /**
     * Get course locations
     * @param int $courseId Course ID
     * @return array Locations
     */
    public function getCourseLocations($courseId) {
        return $this->db->fetchAll(
            "SELECT l.*, c.city_name, s.state_name
             FROM course_locations cl
             JOIN locations l ON cl.location_id = l.location_id
             LEFT JOIN cities c ON l.city_id = c.city_id
             LEFT JOIN states s ON c.state_id = s.state_id
             WHERE cl.course_id = ?
             ORDER BY l.location_name ASC",
            [$courseId]
        );
    }
    
    /**
     * Update course locations
     * @param int $courseId Course ID
     * @param array $locationIds Location IDs
     * @return bool True if update successful
     */
    public function updateCourseLocations($courseId, $locationIds) {
        // Validate inputs
        $courseId = (int)$courseId;
        if ($courseId <= 0) {
            error_log("Invalid course ID: {$courseId}");
            return false;
        }
        
        if (empty($locationIds) || !is_array($locationIds)) {
            error_log("No locations provided or invalid format for course ID: {$courseId}");
            return false;
        }
        
        error_log("Starting updateCourseLocations for course ID: {$courseId} with locations: " . implode(',', $locationIds));
        
        // Start transaction
        $this->db->beginTransaction();
        
        try {
            // Check if course exists
            $courseExists = $this->db->fetchOne(
                "SELECT COUNT(*) FROM courses WHERE course_id = ?",
                [$courseId]
            );
            
            if (!$courseExists) {
                error_log("Course ID {$courseId} does not exist");
                throw new Exception("Course not found");
            }
            
            // Delete existing course locations
            $deleteResult = $this->db->delete('course_locations', 'course_id = ?', [$courseId]);
            error_log("Deleted existing course locations for course ID: {$courseId}, Result: " . ($deleteResult ? 'Success' : 'No rows affected'));
            
            // Insert new course locations
            $insertedCount = 0;
            foreach ($locationIds as $locationId) {
                // Make sure locationId is an integer
                $locationId = (int)$locationId;
                
                if ($locationId <= 0) {
                    error_log("Invalid location ID: {$locationId}, skipping");
                    continue;
                }
                
                // Check if location exists
                $locationExists = $this->db->fetchOne(
                    "SELECT COUNT(*) FROM locations WHERE location_id = ?",
                    [$locationId]
                );
                
                if (!$locationExists) {
                    error_log("Location ID {$locationId} does not exist in the database");
                    continue; // Skip this location
                }
                
                // Check if this course-location pair already exists
                $pairExists = $this->db->fetchOne(
                    "SELECT COUNT(*) FROM course_locations WHERE course_id = ? AND location_id = ?",
                    [$courseId, $locationId]
                );
                
                if ($pairExists) {
                    error_log("Course-location pair already exists for course ID: {$courseId}, location ID: {$locationId}");
                    $insertedCount++;
                    continue;
                }
                
                // Insert into course_locations table
                $insertId = $this->db->insert('course_locations', [
                    'course_id' => $courseId,
                    'location_id' => $locationId
                ]);
                
                if ($insertId) {
                    $insertedCount++;
                    error_log("Inserted course location: course ID: {$courseId}, location ID: {$locationId}, insert ID: {$insertId}");
                } else {
                    error_log("Failed to insert course location: course ID: {$courseId}, location ID: {$locationId}, Error: " . $this->db->getLastError());
                }
            }
            
            // Commit transaction
            $this->db->commit();
            
            error_log("Successfully updated course locations for course ID: {$courseId}. Inserted {$insertedCount} locations.");
            return true;
        } catch (Exception $e) {
            // Rollback transaction
            $this->db->rollback();
            // Log the error
            error_log("Error updating course locations for course ID: {$courseId}. Exception: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get success stories of a coaching center
     * @param int $coachingId Coaching center ID
     * @return array Success stories
     */
    public function getSuccessStories($coachingId) {
        return $this->db->fetchAll(
            "SELECT ss.*, c.course_name
             FROM success_stories ss
             LEFT JOIN courses c ON ss.course_id = c.course_id
             WHERE ss.coaching_id = ? AND ss.status = 'active'
             ORDER BY ss.is_featured DESC, ss.year DESC, ss.display_order ASC
             LIMIT 10",
            [$coachingId]
        );
    }
    
    /**
     * Get reviews of a coaching center
     * @param int $coachingId Coaching center ID
     * @return array Reviews
     */
    public function getReviews($coachingId) {
        return $this->db->fetchAll(
            "SELECT r.*, u.username, u.first_name, u.last_name, u.profile_image
             FROM reviews r
             LEFT JOIN users u ON r.user_id = u.user_id
             WHERE r.coaching_id = ? AND r.status = 'approved'
             ORDER BY r.created_at DESC
             LIMIT 10",
            [$coachingId]
        );
    }
    
    /**
     * Get total count of coaching centers
     * @param string $status Optional status filter (active, pending, inactive)
     * @return int Total count
     */
    public function getTotalCount($status = '') {
        // Log the method call to our new log file
        error_log(date('Y-m-d H:i:s') . " - getTotalCount method called with status: " . ($status ?: 'all') . "\n", 3, BASE_PATH . '/logs/admin_dashboard.log');
        
        $where = '';
        $params = [];
        
        if (!empty($status)) {
            $where = 'status = ?';
            $params = [$status];
        }
        
        // Log the SQL query
        $sql = "SELECT COUNT(*) as count FROM coaching_centers" . (!empty($where) ? " WHERE $where" : "");
        error_log(date('Y-m-d H:i:s') . " - SQL Query: " . $sql . "\n", 3, BASE_PATH . '/logs/admin_dashboard.log');
        
        try {
            return $this->db->count('coaching_centers', $where, $params);
        } catch (Exception $e) {
            // Log any errors
            error_log(date('Y-m-d H:i:s') . " - Error in getTotalCount: " . $e->getMessage() . "\n", 3, BASE_PATH . '/logs/admin_dashboard.log');
            return 0;
        }
    }
    
    /**
     * Get recent coaching centers
     * @param int $limit Number of coaching centers to return
     * @return array Recent coaching centers
     */
    public function getRecent($limit = 5) {
        try {
            // Log the method call
            error_log(date('Y-m-d H:i:s') . " - getRecent method called with limit: " . $limit . "\n", 3, BASE_PATH . '/logs/admin_dashboard.log');
            
            $sql = "SELECT c.*, 
                           ci.city_name, 
                           s.state_name
                    FROM coaching_centers c
                    LEFT JOIN cities ci ON c.city_id = ci.city_id
                    LEFT JOIN states s ON c.state_id = s.state_id
                    ORDER BY c.created_at DESC
                    LIMIT ?";
            
            // Log the SQL query
            error_log(date('Y-m-d H:i:s') . " - SQL Query: " . $sql . "\n", 3, BASE_PATH . '/logs/admin_dashboard.log');
            
            return $this->db->fetchAll($sql, [$limit]);
        } catch (Exception $e) {
            // Log any errors
            error_log(date('Y-m-d H:i:s') . " - Error in getRecent: " . $e->getMessage() . "\n", 3, BASE_PATH . '/logs/admin_dashboard.log');
            return [];
        }
    }
    
    /**
     * Get facilities for a coaching center
     * @param int $coachingId Coaching center ID
     * @return array Facilities
     */
    public function getFacilities($coachingId) {
        return $this->db->fetchAll(
            "SELECT f.* 
             FROM facilities f
             JOIN coaching_facilities cf ON f.facility_id = cf.facility_id
             WHERE cf.coaching_id = ?
             ORDER BY f.facility_name ASC",
            [$coachingId]
        );
    }
    
    }
