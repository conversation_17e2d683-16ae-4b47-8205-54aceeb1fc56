<div class="admin-sidebar" id="adminSidebar">
    <div class="sidebar-header">
        <button class="sidebar-toggle" id="sidebarToggle">
            <i class="fas fa-bars"></i>
        </button>
    </div>
    
    <div class="sidebar-menu">
        <div class="menu-section">
            <div class="menu-section-title">Main</div>
            <ul class="nav flex-column">
                <li class="menu-item">
                    <a href="dashboard.php" class="menu-link <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>">
                        <span class="menu-icon"><i class="fas fa-tachometer-alt"></i></span>
                        <span class="menu-text">Dashboard</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#coachingSubmenu" class="menu-link" data-bs-toggle="collapse" aria-expanded="false">
                        <span class="menu-icon"><i class="fas fa-building"></i></span>
                        <span class="menu-text">Coaching Centers</span>
                        <span class="menu-arrow"><i class="fas fa-chevron-right"></i></span>
                    </a>
                    <div class="collapse submenu" id="coachingSubmenu">
                        <ul class="nav flex-column">
                            <li class="menu-item">
                                <a href="coaching-centers.php" class="submenu-link <?php echo basename($_SERVER['PHP_SELF']) == 'coaching-centers.php' ? 'active' : ''; ?>">
                                    All Coaching Centers
                                </a>
                            </li>
                            <li class="menu-item">
                                <a href="coaching-add.php" class="submenu-link <?php echo basename($_SERVER['PHP_SELF']) == 'coaching-add.php' ? 'active' : ''; ?>">
                                    Add New
                                </a>
                            </li>
                            <li class="menu-item">
                                <a href="coaching-categories.php" class="submenu-link <?php echo basename($_SERVER['PHP_SELF']) == 'coaching-categories.php' ? 'active' : ''; ?>">
                                    Categories
                                </a>
                            </li>
                            <li class="menu-item">
                                <a href="coaching-reviews.php" class="submenu-link <?php echo basename($_SERVER['PHP_SELF']) == 'coaching-reviews.php' ? 'active' : ''; ?>">
                                    Reviews
                                </a>
                            </li>
                            <li class="menu-item">
                                <a href="facilities.php" class="submenu-link <?php echo basename($_SERVER['PHP_SELF']) == 'facilities.php' ? 'active' : ''; ?>">
                                    Facilities
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li class="menu-item">
                    <a href="#locationSubmenu" class="menu-link" data-bs-toggle="collapse" aria-expanded="false">
                        <span class="menu-icon"><i class="fas fa-map-marker-alt"></i></span>
                        <span class="menu-text">Locations</span>
                        <span class="menu-arrow"><i class="fas fa-chevron-right"></i></span>
                    </a>
                    <div class="collapse submenu" id="locationSubmenu">
                        <ul class="nav flex-column">
                            <li class="menu-item">
                                <a href="states.php" class="submenu-link <?php echo basename($_SERVER['PHP_SELF']) == 'states.php' ? 'active' : ''; ?>">
                                    States
                                </a>
                            </li>
                            <li class="menu-item">
                                <a href="cities.php" class="submenu-link <?php echo basename($_SERVER['PHP_SELF']) == 'cities.php' ? 'active' : ''; ?>">
                                    Cities
                                </a>
                            </li>
                            <li class="menu-item">
                                <a href="locations.php" class="submenu-link <?php echo basename($_SERVER['PHP_SELF']) == 'locations.php' ? 'active' : ''; ?>">
                                    Locations
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li class="menu-item">
                    <a href="users.php" class="menu-link <?php echo basename($_SERVER['PHP_SELF']) == 'users.php' ? 'active' : ''; ?>">
                        <span class="menu-icon"><i class="fas fa-users"></i></span>
                        <span class="menu-text">Users</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="enquiries.php" class="menu-link <?php echo basename($_SERVER['PHP_SELF']) == 'enquiries.php' ? 'active' : ''; ?>">
                        <span class="menu-icon"><i class="fas fa-envelope"></i></span>
                        <span class="menu-text">Enquiries</span>
                        <?php if (isset($newEnquiries) && $newEnquiries > 0): ?>
                            <span class="badge bg-danger rounded-pill ms-auto"><?php echo $newEnquiries; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
            </ul>
        </div>
        
        <div class="menu-section">
            <div class="menu-section-title">Content</div>
            <ul class="nav flex-column">
                <li class="menu-item">
                    <a href="#blogSubmenu" class="menu-link" data-bs-toggle="collapse" aria-expanded="false">
                        <span class="menu-icon"><i class="fas fa-blog"></i></span>
                        <span class="menu-text">Blog</span>
                        <span class="menu-arrow"><i class="fas fa-chevron-right"></i></span>
                    </a>
                    <div class="collapse submenu" id="blogSubmenu">
                        <ul class="nav flex-column">
                            <li class="menu-item">
                                <a href="blog-posts.php" class="submenu-link <?php echo basename($_SERVER['PHP_SELF']) == 'blog-posts.php' ? 'active' : ''; ?>">
                                    All Posts
                                </a>
                            </li>
                            <li class="menu-item">
                                <a href="blog-add.php" class="submenu-link <?php echo basename($_SERVER['PHP_SELF']) == 'blog-add.php' ? 'active' : ''; ?>">
                                    Add New
                                </a>
                            </li>
                            <li class="menu-item">
                                <a href="blog-categories.php" class="submenu-link <?php echo basename($_SERVER['PHP_SELF']) == 'blog-categories.php' ? 'active' : ''; ?>">
                                    Categories
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li class="menu-item">
                    <a href="pages.php" class="menu-link <?php echo basename($_SERVER['PHP_SELF']) == 'pages.php' ? 'active' : ''; ?>">
                        <span class="menu-icon"><i class="fas fa-file-alt"></i></span>
                        <span class="menu-text">Pages</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="testimonials.php" class="menu-link <?php echo basename($_SERVER['PHP_SELF']) == 'testimonials.php' ? 'active' : ''; ?>">
                        <span class="menu-icon"><i class="fas fa-quote-right"></i></span>
                        <span class="menu-text">Testimonials</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="faqs.php" class="menu-link <?php echo basename($_SERVER['PHP_SELF']) == 'faqs.php' ? 'active' : ''; ?>">
                        <span class="menu-icon"><i class="fas fa-question-circle"></i></span>
                        <span class="menu-text">FAQs</span>
                    </a>
                </li>
            </ul>
        </div>
        
        <div class="menu-section">
            <div class="menu-section-title">Marketing</div>
            <ul class="nav flex-column">
                <li class="menu-item">
                    <a href="seo.php" class="menu-link <?php echo basename($_SERVER['PHP_SELF']) == 'seo.php' ? 'active' : ''; ?>">
                        <span class="menu-icon"><i class="fas fa-search"></i></span>
                        <span class="menu-text">SEO Settings</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="email-marketing.php" class="menu-link <?php echo basename($_SERVER['PHP_SELF']) == 'email-marketing.php' ? 'active' : ''; ?>">
                        <span class="menu-icon"><i class="fas fa-envelope-open-text"></i></span>
                        <span class="menu-text">Email Marketing</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="analytics.php" class="menu-link <?php echo basename($_SERVER['PHP_SELF']) == 'analytics.php' ? 'active' : ''; ?>">
                        <span class="menu-icon"><i class="fas fa-chart-bar"></i></span>
                        <span class="menu-text">Analytics</span>
                    </a>
                </li>
            </ul>
        </div>
        
        <div class="menu-section">
            <div class="menu-section-title">Settings</div>
            <ul class="nav flex-column">
                <li class="menu-item">
                    <a href="general-settings.php" class="menu-link <?php echo basename($_SERVER['PHP_SELF']) == 'general-settings.php' ? 'active' : ''; ?>">
                        <span class="menu-icon"><i class="fas fa-cog"></i></span>
                        <span class="menu-text">General Settings</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="appearance.php" class="menu-link <?php echo basename($_SERVER['PHP_SELF']) == 'appearance.php' ? 'active' : ''; ?>">
                        <span class="menu-icon"><i class="fas fa-palette"></i></span>
                        <span class="menu-text">Appearance</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="admin-users.php" class="menu-link <?php echo basename($_SERVER['PHP_SELF']) == 'admin-users.php' ? 'active' : ''; ?>">
                        <span class="menu-icon"><i class="fas fa-user-shield"></i></span>
                        <span class="menu-text">Admin Users</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="backup.php" class="menu-link <?php echo basename($_SERVER['PHP_SELF']) == 'backup.php' ? 'active' : ''; ?>">
                        <span class="menu-icon"><i class="fas fa-database"></i></span>
                        <span class="menu-text">Backup & Restore</span>
                    </a>
                </li>
            </ul>
        </div>
    </div>
    
    <div class="sidebar-footer">
        <p>&copy; <?php echo date('Y'); ?> <?php echo $settings->getSiteName(); ?></p>
        <p>Version 1.0.0</p>
    </div>
</div>