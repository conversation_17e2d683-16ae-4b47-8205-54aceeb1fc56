<?php
/**
 * AJAX handler to get cities by state
 */
require_once '../../includes/autoload.php';

// Check if user is logged in
Auth::requireCoachingOwner();

// Get state ID
$stateId = (int)($_POST['state_id'] ?? 0);

if ($stateId <= 0) {
    header('Content-Type: application/json');
    echo json_encode([]);
    exit;
}

// Get cities
$cityObj = new City();
$cities = $cityObj->getByStateId($stateId, 'active');

// Return JSON response
header('Content-Type: application/json');
echo json_encode($cities);