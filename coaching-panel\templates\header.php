<?php
// Get coaching center data
$coachingId = $_SESSION['coaching_id'] ?? 0;

// Initialize user data
$user = ['first_name' => 'User', 'last_name' => '', 'email' => '', 'profile_image' => ''];

// Try to get user data if User class exists
if (class_exists('User')) {
    try {
        $userObj = new User();
        $userData = $userObj->getUserById($_SESSION['user_id'] ?? 0);
        if ($userData && is_array($userData)) {
            $user = array_merge($user, $userData);
        }
    } catch (Exception $e) {
        // Ignore errors and use default user data
    }
}

?>

<header class="dashboard-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="d-flex align-items-center">
                    <button class="btn btn-link text-dark d-md-none me-3" id="mobileSidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="page-title d-none d-md-block">
                        <h4 class="mb-0"><?php echo $pageTitle ?? 'Dashboard'; ?></h4>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="header-right d-flex align-items-center justify-content-end">
                    <!-- View Website Button -->
                    <div class="me-3">
                        <?php 
                        $coachingObj = new CoachingCenter();
                        $coaching = $coachingObj->getById($coachingId);
                        if ($coaching): 
                        ?>
                            <a href="<?php echo getCoachingUrl($coaching['slug']); ?>" class="btn btn-sm btn-outline-primary" target="_blank">
                                <i class="fas fa-external-link-alt me-1"></i> View Your Page
                            </a>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Notifications -->
                    <div class="dropdown me-3">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-bell"></i>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header">Notifications</h6></li>
                            <?php if ($unreadInquiries > 0): ?>
                                <li>
                                    <a class="dropdown-item" href="enquiries.php">
                                        <i class="fas fa-envelope me-2 text-primary"></i>
                                        You have <?php echo $unreadInquiries; ?> new enquiries
                                    </a>
                                </li>
                            <?php else: ?>
                                <li><div class="dropdown-item">No new notifications</div></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-center" href="enquiries.php">View All Enquiries</a></li>
                        </ul>
                    </div>
                    
                    <!-- User Profile -->
                    <div class="dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="user-avatar me-2">
                                <?php if (!empty($user['profile_image'])): ?>
                                    <img src="<?php echo getUploadUrl($user['profile_image']); ?>" alt="<?php echo htmlspecialchars($user['first_name']); ?>" class="rounded-circle" width="32" height="32">
                                <?php else: ?>
                                    <div class="avatar-placeholder rounded-circle bg-primary text-white">
                                        <?php echo strtoupper(substr($user['first_name'] ?? 'U', 0, 1)); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="user-name d-none d-md-block">
                                <?php echo htmlspecialchars($user['first_name'] ?? 'User'); ?>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <div class="dropdown-item-text">
                                    <div class="d-flex align-items-center">
                                        <div class="user-avatar me-3">
                                            <?php if (!empty($user['profile_image'])): ?>
                                                <img src="<?php echo getUploadUrl($user['profile_image']); ?>" alt="<?php echo htmlspecialchars($user['first_name']); ?>" class="rounded-circle" width="48" height="48">
                                            <?php else: ?>
                                                <div class="avatar-placeholder rounded-circle bg-primary text-white" style="width: 48px; height: 48px; font-size: 20px; display: flex; align-items: center; justify-content: center;">
                                                    <?php echo strtoupper(substr($user['first_name'] ?? 'U', 0, 1)); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div>
                                            <h6 class="mb-0"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></h6>
                                            <small class="text-muted"><?php echo htmlspecialchars($user['email']); ?></small>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i> My Profile</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>