<?php
/**
 * Submit Enquiry Handler
 * This file processes the enquiry form submissions from the coaching center detail page
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include required files
require_once 'includes/autoload.php';



try {
    // Get coaching ID and validate
    $coaching_id = isset($_POST['coaching_id']) ? (int)$_POST['coaching_id'] : 0;
    

    
    // Get slug from form submission
    $slug = isset($_POST['slug']) ? $_POST['slug'] : '';
    
    // Get coaching center details
    $coachingObj = new CoachingCenter();
    $coaching = $coachingObj->getById($coaching_id);
    

    

    
    // Construct redirect URL
    $redirect_url = "center.php?slug=" . urlencode($slug);
    
    // Validate form data
    $errors = [];
    if (empty($_POST['name'])) {
        $errors[] = "Name is required";
    }
    
    if (empty($_POST['email']) || !filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Valid email is required";
    }
    
    if (empty($_POST['phone'])) {
        $errors[] = "Phone is required";
    }
    
    // If there are validation errors, redirect back with errors
    if (!empty($errors)) {
        $_SESSION['enquiry_errors'] = $errors;
        header('Location: ' . $redirect_url . "&enquiry=error");
        exit;
    }
    
    // Save to database
    $db = Database::getInstance();
    $sql = "INSERT INTO enquiries (coaching_id, course_id, location_id, name, email, phone, message, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
    
    $params = [
        $coaching_id,
        isset($_POST['course']) && !empty($_POST['course']) ? (int)$_POST['course'] : null,
        isset($_POST['location_id']) && !empty($_POST['location_id']) ? (int)$_POST['location_id'] : null,
        sanitize($_POST['name']),
        sanitize($_POST['email']),
        sanitize($_POST['phone']),
        isset($_POST['message']) ? sanitize($_POST['message']) : ''
    ];
    
    $result = $db->query($sql, $params);
    
    if (!$result) {
        $_SESSION['enquiry_error'] = "Failed to submit enquiry. Please try again.";
        header('Location: ' . $redirect_url . "&enquiry=error");
        exit;
    }
    
    // Success
    unset($_SESSION['enquiry_errors']);
    header('Location: ' . $redirect_url . "&enquiry=success");
    exit;
    
} 