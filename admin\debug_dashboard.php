<?php
/**
 * Debug Dashboard Script
 */
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    echo "<h2>1. Testing Autoload</h2>";
    require_once '../includes/autoload.php';
    echo "✓ Autoload successful<br>";
    
    echo "<h2>2. Testing Database Connection</h2>";
    $db = Database::getInstance()->getConnection();
    if (!$db) {
        throw new Exception("Failed to connect to database");
    }
    echo "✓ Database connection successful<br>";
    
    echo "<h2>3. Testing Settings</h2>";
    $settings = Settings::getInstance();
    if (!$settings) {
        throw new Exception("Failed to initialize settings");
    }
    echo "✓ Settings initialized<br>";
    
    echo "<h2>4. Testing User Authentication</h2>";
    $user = new User();
    echo "User logged in: " . ($user->isLoggedIn() ? 'Yes' : 'No') . "<br>";
    echo "User is admin: " . ($user->isAdmin() ? 'Yes' : 'No') . "<br>";
    
    if (!$user->isLoggedIn() || !$user->isAdmin()) {
        echo "<p style='color: orange;'>⚠️ User not logged in or not admin - this might be the issue</p>";
    }
    
    echo "<h2>5. Testing Classes</h2>";
    
    // Test CoachingCenter class
    $coachingObj = new CoachingCenter();
    $totalCoachings = $coachingObj->getTotalCount();
    echo "✓ CoachingCenter class works - Total: $totalCoachings<br>";
    
    // Test User class methods
    $userObj = new User();
    $totalUsers = $userObj->getTotalCount();
    echo "✓ User class works - Total: $totalUsers<br>";
    
    // Test Review class
    $reviewObj = new Review();
    $totalReviews = $reviewObj->getTotalCount();
    echo "✓ Review class works - Total: $totalReviews<br>";
    
    // Test Enquiry class
    $enquiryObj = new Enquiry();
    $totalEnquiries = $enquiryObj->getTotalCount();
    echo "✓ Enquiry class works - Total: $totalEnquiries<br>";
    
    // Test Admin class
    $adminObj = new Admin();
    $recentActivities = $adminObj->getRecentActivities(5);
    echo "✓ Admin class works - Activities: " . count($recentActivities) . "<br>";
    
    echo "<h2>6. Testing Template Files</h2>";
    
    if (file_exists('templates/sidebar.php')) {
        echo "✓ Sidebar template exists<br>";
    } else {
        echo "✗ Sidebar template missing<br>";
    }
    
    if (file_exists('templates/header.php')) {
        echo "✓ Header template exists<br>";
    } else {
        echo "✗ Header template missing<br>";
    }
    
    echo "<h2>7. Testing CSS Files</h2>";
    
    if (file_exists('assets/css/admin.css')) {
        echo "✓ Admin CSS exists<br>";
    } else {
        echo "✗ Admin CSS missing<br>";
    }
    
    echo "<h2>All tests completed successfully!</h2>";
    echo "<p><a href='dashboard.php'>Try Dashboard Again</a></p>";
    
} catch (Exception $e) {
    echo '<div style="color:red;padding:20px;border:2px solid red;margin:20px;">'
        . '<h2>Error Found!</h2>'
        . '<p><strong>Error:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>'
        . '<p><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</p>'
        . '<p><strong>Line:</strong> ' . htmlspecialchars($e->getLine()) . '</p>'
        . '<p><strong>Stack Trace:</strong></p>'
        . '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>'
        . '</div>';
}
?>