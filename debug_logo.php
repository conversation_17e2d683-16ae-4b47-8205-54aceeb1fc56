<?php
require_once 'includes/autoload.php';

// Get settings
$settings = Settings::getInstance();
$logoPath = $settings->getSiteLogo();
$assetUrl = getAssetUrl($logoPath);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logo Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .debug-info {
            background-color: #f5f5f5;
            padding: 15px;
            border: 1px solid #ddd;
            margin-bottom: 20px;
            font-family: monospace;
        }
        .logo-container {
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid #ccc;
        }
        .logo {
            max-width: 200px;
            height: 70px;
            object-fit: contain;
            border: 1px dashed red;
        }
    </style>
</head>
<body>
    <h1>Logo Debug Page</h1>
    
    <div class="debug-info">
        <h2>Debug Information</h2>
        <p>Logo Path from Settings: <?php echo htmlspecialchars($logoPath); ?></p>
        <p>Asset URL: <?php echo htmlspecialchars($assetUrl); ?></p>
        <p>Full Server Path: <?php echo htmlspecialchars(__DIR__ . '/' . $logoPath); ?></p>
        <p>File Exists: <?php echo file_exists(__DIR__ . '/' . $logoPath) ? 'Yes' : 'No'; ?></p>
        <p>File Size: <?php echo file_exists(__DIR__ . '/' . $logoPath) ? filesize(__DIR__ . '/' . $logoPath) . ' bytes' : 'N/A'; ?></p>
        <p>BASE_URL: <?php echo htmlspecialchars(BASE_URL); ?></p>
        <p>ASSETS_URL: <?php echo htmlspecialchars(ASSETS_URL); ?></p>
    </div>
    
    <div class="logo-container">
        <h2>Logo using getAssetUrl()</h2>
        <img src="<?php echo htmlspecialchars($assetUrl); ?>" alt="Logo with Asset URL" class="logo">
    </div>
    
    <div class="logo-container">
        <h2>Logo with direct path</h2>
        <img src="<?php echo htmlspecialchars($logoPath); ?>" alt="Logo Direct Path" class="logo">
    </div>
    
    <div class="logo-container">
        <h2>Logo with absolute path</h2>
        <img src="/coaching/<?php echo htmlspecialchars($logoPath); ?>" alt="Logo Absolute Path" class="logo">
    </div>
</body>
</html>